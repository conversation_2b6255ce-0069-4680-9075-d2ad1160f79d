"""
SeleniumBase Enhanced Driver Class
Migration from selenium/selenium-wire to SeleniumBase with advanced stealth capabilities
Phase 1.3: Project Structure Changes - New main file with SeleniumBase driver
"""

import os
import json
import random
import logging
import subprocess
import shutil
import string
import time
import math
import numpy as np
from time import sleep
from datetime import datetime
from typing import Dict, Optional, Any, Tuple, List
from dataclasses import dataclass, asdict
import threading
import sqlite3
from pathlib import Path
import pickle
import base64
from urllib.parse import urlparse
from random import uniform, randint, choice

# SeleniumBase imports
try:
    from seleniumbase import Driver as SBDriver
    from seleniumbase.core.browser_launcher import get_driver
    SELENIUMBASE_AVAILABLE = True
except ImportError:
    print("WARNING: SeleniumBase not available, falling back to standard selenium")
    SBDriver = None
    get_driver = None
    SELENIUMBASE_AVAILABLE = False





# Standard selenium imports for compatibility
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, TimeoutException

# Updated User Agents for Chrome ********* - Validated and Current
CHROME_139_USER_AGENTS = [
    # Windows 10/11 Chrome *********
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

    # Windows with Edge integration
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',

    # macOS Chrome *********
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

    # Linux Chrome *********
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (X11; Linux i686) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

    # ChromeOS Chrome *********
    'Mozilla/5.0 (X11; CrOS x86_64 15633.69.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (X11; CrOS x86_64 15633.57.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (X11; CrOS x86_64 15633.84.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (X11; CrOS x86_64 15633.89.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (X11; CrOS x86_64 15633.92.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

    # Windows with different NT versions
    'Mozilla/5.0 (Windows NT 11.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',

    # Mobile Chrome ********* (for variety)
    'Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    'Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) CriOS/********* Mobile/15E148 Safari/604.1',

    # Additional Windows variations
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********',
]

# Fingerprint protection imports
import numpy as np
from PIL import Image
import pyautogui

# Configuration paths (same as original)
home = os.path.dirname(os.path.realpath(__file__)).replace("\\", "/")
Files_home = home.replace('PyFiles','')

# Use the same profile directory as legacy driver for consistency
# This ensures both drivers use the same profile location
profile_home = f"{home.replace('PyFiles','')}Profiles"
settings_path = f"{home}/json/settings.json"
proxy_file = f"{home}/proxy.txt"


# ========== BEHAVIORAL SIMULATION SYSTEM ==========

class BehavioralSimulator:
    """
    Human-like behavioral simulation for enhanced stealth
    Implements mouse movements, typing patterns, scroll behavior, and click timing
    """

    def __init__(self, driver, profile_seed=None):
        """Initialize behavioral simulator with driver and profile-specific seed"""
        self.driver = driver
        self.profile_seed = profile_seed or random.randint(1000, 9999)
        self.rng = random.Random(self.profile_seed)

        # Initialize PyAutoGUI for enhanced mouse movements
        try:
            import pyautogui
            self.pyautogui = pyautogui
            self.pyautogui.FAILSAFE = True
            self.pyautogui.PAUSE = 0.01  # Minimal pause for smooth movements
            self.pyautogui_available = True
        except ImportError:
            self.pyautogui = None
            self.pyautogui_available = False

        # Behavioral characteristics based on profile seed
        self._init_behavioral_profile()

    def _init_behavioral_profile(self):
        """Initialize profile-specific behavioral characteristics"""
        # Mouse movement characteristics
        self.mouse_speed_base = self.rng.uniform(0.8, 1.5)  # Base movement speed multiplier
        self.mouse_precision = self.rng.uniform(0.7, 0.95)  # How precise movements are
        self.mouse_overshoot_chance = self.rng.uniform(0.1, 0.3)  # Chance of overshooting target

        # Typing characteristics
        self.typing_speed_wpm = self.rng.uniform(35, 85)  # Words per minute
        self.typing_accuracy = self.rng.uniform(0.92, 0.98)  # Accuracy rate
        self.pause_frequency = self.rng.uniform(0.05, 0.15)  # Frequency of thinking pauses

        # Scroll characteristics
        self.scroll_speed_base = self.rng.uniform(0.8, 1.3)  # Base scroll speed
        self.scroll_momentum = self.rng.uniform(0.6, 0.9)  # How much momentum affects scrolling

        # Click characteristics
        self.click_duration_base = self.rng.uniform(0.08, 0.15)  # Base click duration
        self.pre_click_hover_time = self.rng.uniform(0.2, 0.8)  # Time to hover before clicking

    def bezier_curve(self, start, end, control_points=None, steps=50):
        """Generate bezier curve points for smooth mouse movement"""
        if control_points is None:
            # Generate random control points for natural curve
            mid_x = (start[0] + end[0]) / 2 + self.rng.uniform(-50, 50)
            mid_y = (start[1] + end[1]) / 2 + self.rng.uniform(-30, 30)
            control_points = [(mid_x, mid_y)]

        points = []
        for i in range(steps + 1):
            t = i / steps

            if len(control_points) == 1:
                # Quadratic bezier
                x = (1-t)**2 * start[0] + 2*(1-t)*t * control_points[0][0] + t**2 * end[0]
                y = (1-t)**2 * start[1] + 2*(1-t)*t * control_points[0][1] + t**2 * end[1]
            else:
                # Linear interpolation fallback
                x = start[0] + t * (end[0] - start[0])
                y = start[1] + t * (end[1] - start[1])

            points.append((int(x), int(y)))

        return points

    def human_mouse_move(self, element, offset_x=0, offset_y=0):
        """Move mouse to element with human-like movement pattern"""
        try:
            # Get current mouse position (approximate)
            current_pos = (self.rng.randint(0, 1920), self.rng.randint(0, 1080))

            # Get target element position
            location = element.location
            size = element.size

            # Calculate target position with some randomness
            target_x = location['x'] + size['width'] // 2 + offset_x
            target_y = location['y'] + size['height'] // 2 + offset_y

            # Add some randomness to target position
            target_x += self.rng.uniform(-5, 5)
            target_y += self.rng.uniform(-5, 5)

            target_pos = (target_x, target_y)

            # Generate bezier curve for movement
            curve_points = self.bezier_curve(current_pos, target_pos)

            # Simulate movement with ActionChains
            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(self.driver)

            # Move to element with some intermediate steps for realism
            steps = min(len(curve_points), 10)  # Limit steps for performance
            for i in range(0, len(curve_points), len(curve_points) // steps):
                if i < len(curve_points):
                    # Move to intermediate position
                    actions.move_by_offset(
                        curve_points[i][0] - current_pos[0],
                        curve_points[i][1] - current_pos[1]
                    )
                    current_pos = curve_points[i]

                    # Add small delay for realistic movement
                    movement_delay = self.rng.uniform(0.01, 0.03) / self.mouse_speed_base
                    time.sleep(movement_delay)

            # Final move to exact element
            actions.move_to_element_with_offset(element, offset_x, offset_y)
            actions.perform()

            # Add small random micro-movements
            self._add_micro_movements(element)

        except Exception:
            # Fallback to simple move
            try:
                from selenium.webdriver.common.action_chains import ActionChains
                ActionChains(self.driver).move_to_element_with_offset(element, offset_x, offset_y).perform()
            except:
                pass

    def _add_micro_movements(self, element):
        """Add small random movements to simulate human imprecision"""
        try:
            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(self.driver)

            # Add 1-3 micro movements
            num_movements = self.rng.randint(1, 3)
            for _ in range(num_movements):
                offset_x = self.rng.uniform(-2, 2)
                offset_y = self.rng.uniform(-2, 2)
                actions.move_by_offset(offset_x, offset_y)
                time.sleep(self.rng.uniform(0.01, 0.02))

            actions.perform()
        except:
            pass

    def human_type(self, element, text, clear_first=True):
        """Type text with human-like patterns including variable speeds and pauses"""
        try:
            if clear_first:
                element.clear()
                time.sleep(self.rng.uniform(0.1, 0.3))

            # Calculate base delay between keystrokes based on WPM
            # Average word length is ~5 characters, so chars per minute = WPM * 5
            chars_per_minute = self.typing_speed_wpm * 5
            base_delay = 60.0 / chars_per_minute

            typed_text = ""
            i = 0

            while i < len(text):
                char = text[i]

                # Simulate typing errors occasionally
                if self.rng.random() > self.typing_accuracy and len(typed_text) > 0:
                    # Type wrong character then correct it
                    wrong_char = self.rng.choice('abcdefghijklmnopqrstuvwxyz')
                    element.send_keys(wrong_char)
                    typed_text += wrong_char

                    # Pause to "notice" the error
                    time.sleep(self.rng.uniform(0.2, 0.5))

                    # Backspace to correct
                    from selenium.webdriver.common.keys import Keys
                    element.send_keys(Keys.BACKSPACE)
                    typed_text = typed_text[:-1]
                    time.sleep(self.rng.uniform(0.1, 0.2))

                # Type the correct character
                element.send_keys(char)
                typed_text += char

                # Variable delay based on character type
                if char == ' ':
                    # Longer pause after words
                    delay = base_delay * self.rng.uniform(1.5, 2.5)
                elif char in '.,!?;:':
                    # Pause after punctuation
                    delay = base_delay * self.rng.uniform(1.2, 2.0)
                elif char.isupper():
                    # Slight pause for capital letters (shift key)
                    delay = base_delay * self.rng.uniform(1.1, 1.4)
                else:
                    # Normal character delay with variation
                    delay = base_delay * self.rng.uniform(0.7, 1.3)

                # Occasional thinking pauses
                if self.rng.random() < self.pause_frequency:
                    delay += self.rng.uniform(0.5, 2.0)

                time.sleep(delay)
                i += 1

        except Exception as e:
            # Fallback to simple typing
            try:
                if clear_first:
                    element.clear()
                element.send_keys(text)
            except:
                pass

    def human_scroll(self, direction='down', amount=3, element=None):
        """Scroll with human-like patterns including momentum and variable speeds"""
        try:
            from selenium.webdriver.common.action_chains import ActionChains
            from selenium.webdriver.common.keys import Keys

            if element is None:
                # Scroll on the page body
                element = self.driver.find_element("tag name", "body")

            actions = ActionChains(self.driver)
            actions.move_to_element(element)

            # Determine scroll direction
            scroll_key = Keys.DOWN if direction.lower() == 'down' else Keys.UP

            # Variable scroll amounts with momentum simulation
            for i in range(amount):
                # Simulate momentum - faster at start, slower at end
                momentum_factor = 1.0 - (i / amount) * self.scroll_momentum
                scroll_delay = (0.1 / self.scroll_speed_base) / momentum_factor

                # Add some randomness to scroll timing
                scroll_delay *= self.rng.uniform(0.8, 1.2)

                # Perform scroll action
                actions.send_keys(scroll_key)
                actions.perform()

                time.sleep(scroll_delay)

                # Occasional pause during scrolling (reading behavior)
                if self.rng.random() < 0.1:  # 10% chance
                    time.sleep(self.rng.uniform(0.5, 1.5))

        except Exception as e:
            # Fallback to simple scroll
            try:
                from selenium.webdriver.common.keys import Keys
                if element is None:
                    element = self.driver.find_element("tag name", "html")

                scroll_key = Keys.DOWN if direction.lower() == 'down' else Keys.UP
                for _ in range(amount):
                    element.send_keys(scroll_key)
                    time.sleep(0.1)
            except:
                pass

    def human_click(self, element, button='left', double_click=False):
        """Click with human-like timing including pre-click hover and realistic intervals"""
        try:
            # Try PyAutoGUI enhanced movement first
            if self.pyautogui_available:
                try:
                    location = element.location
                    size = element.size
                    target_x = location['x'] + size['width'] // 2 + self.rng.uniform(-3, 3)
                    target_y = location['y'] + size['height'] // 2 + self.rng.uniform(-3, 3)

                    # Natural movement to element
                    self.pyautogui_natural_move(target_x, target_y)

                    # Pre-click hover
                    hover_time = self.pre_click_hover_time * self.rng.uniform(0.8, 1.2)
                    time.sleep(hover_time)

                    # Use Selenium click after positioning
                    element.click()
                    return

                except Exception:
                    pass  # Fall back to standard method

            # Standard method - Move to element first with human-like movement
            self.human_mouse_move(element)

            # Pre-click hover time
            hover_time = self.pre_click_hover_time * self.rng.uniform(0.8, 1.2)
            time.sleep(hover_time)

            from selenium.webdriver.common.action_chains import ActionChains
            actions = ActionChains(self.driver)

            if double_click:
                actions.double_click(element)
            else:
                # Simulate mouse down and up with realistic timing
                actions.click_and_hold(element)
                actions.perform()

                # Hold click for realistic duration
                click_duration = self.click_duration_base * self.rng.uniform(0.8, 1.2)
                time.sleep(click_duration)

                # Release click
                actions = ActionChains(self.driver)
                actions.release(element)

            actions.perform()

            # Post-click delay
            post_click_delay = self.rng.uniform(0.1, 0.3)
            time.sleep(post_click_delay)

        except Exception:
            # Fallback to simple click
            try:
                if double_click:
                    from selenium.webdriver.common.action_chains import ActionChains
                    ActionChains(self.driver).double_click(element).perform()
                else:
                    element.click()
            except:
                pass

    def pyautogui_natural_move(self, target_x, target_y, duration=None):
        """Move mouse naturally using PyAutoGUI with curved paths and variable speeds"""
        if not self.pyautogui_available:
            return False

        try:
            # Get current mouse position
            current_x, current_y = self.pyautogui.position()

            # Calculate distance and determine duration
            distance = ((target_x - current_x) ** 2 + (target_y - current_y) ** 2) ** 0.5
            if duration is None:
                # Variable speed based on distance (100-300 pixels per second)
                base_speed = self.rng.uniform(100, 300)
                duration = max(0.1, distance / base_speed)
                duration *= self.rng.uniform(0.2, 0.5)  # Add randomness

            # Use PyAutoGUI's built-in tweening for natural movement
            tween_functions = [self.pyautogui.easeInQuad, self.pyautogui.easeOutQuad,
                             self.pyautogui.easeInOutQuad, self.pyautogui.easeInCubic]
            tween = self.rng.choice(tween_functions)

            # Move with natural curve
            self.pyautogui.moveTo(target_x, target_y, duration=duration, tween=tween)

            # Add small random movements at destination (human imprecision)
            if self.rng.random() < 0.3:  # 30% chance
                offset_x = self.rng.uniform(-2, 2)
                offset_y = self.rng.uniform(-2, 2)
                self.pyautogui.moveRel(offset_x, offset_y, duration=0.1)

            return True

        except Exception:
            return False

    def simulate_reading_movements(self, duration=2.0):
        """Simulate natural mouse movements while reading/analyzing content"""
        if not self.pyautogui_available:
            return

        try:
            screen_width, screen_height = self.pyautogui.size()
            current_x, current_y = self.pyautogui.position()

            # Number of small movements during reading
            num_movements = self.rng.randint(3, 8)
            movement_duration = duration / num_movements

            for _ in range(num_movements):
                # Small movements around current area
                offset_x = self.rng.uniform(-50, 50)
                offset_y = self.rng.uniform(-30, 30)

                new_x = max(50, min(screen_width - 50, current_x + offset_x))
                new_y = max(50, min(screen_height - 50, current_y + offset_y))

                # Move naturally
                self.pyautogui_natural_move(new_x, new_y, duration=self.rng.uniform(0.2, 0.5))

                # Pause between movements
                time.sleep(self.rng.uniform(0.1, movement_duration))

                current_x, current_y = new_x, new_y

        except Exception:
            pass

    def simulate_idle_movements(self):
        """Simulate occasional idle mouse movements during wait times"""
        if not self.pyautogui_available or self.rng.random() > 0.3:  # 30% chance
            return

        try:
            current_x, current_y = self.pyautogui.position()

            # Small idle movement
            offset_x = self.rng.uniform(-20, 20)
            offset_y = self.rng.uniform(-20, 20)

            self.pyautogui.moveRel(offset_x, offset_y, duration=self.rng.uniform(0.5, 1.0))

        except Exception:
            pass



class EnhancedSeleniumBaseDriver:

    @staticmethod
    def get_random_user_agent():
        """
        Get a random Chrome ********* user agent from the validated list.

        Returns:
            str: A random user agent string
        """
        return random.choice(CHROME_139_USER_AGENTS)

    @staticmethod
    def get_desktop_user_agents():
        """
        Get only desktop user agents (excludes mobile).

        Returns:
            list: List of desktop user agents
        """
        return [ua for ua in CHROME_139_USER_AGENTS if 'Mobile' not in ua and 'Android' not in ua and 'iPhone' not in ua]

    @staticmethod
    def get_random_desktop_user_agent():
        """
        Get a random desktop user agent (excludes mobile).

        Returns:
            str: A random desktop user agent string
        """
        desktop_agents = EnhancedSeleniumBaseDriver.get_desktop_user_agents()
        return random.choice(desktop_agents)

    @staticmethod
    def get_weighted_random_desktop_user_agent():
        """
        Get a weighted random desktop user agent with preference for common platforms.
        Windows gets higher weight, followed by macOS, then Linux/ChromeOS.

        Returns:
            str: A weighted random desktop user agent string
        """
        desktop_agents = EnhancedSeleniumBaseDriver.get_desktop_user_agents()

        # Create weights: Windows=3, macOS=2, Linux/ChromeOS=1
        weights = []
        for ua in desktop_agents:
            if 'Windows' in ua:
                weights.append(3)  # Higher weight for Windows
            elif 'Macintosh' in ua:
                weights.append(2)  # Medium weight for macOS
            else:  # Linux, ChromeOS
                weights.append(1)  # Lower weight for others

        return random.choices(desktop_agents, weights=weights, k=1)[0]

    @staticmethod
    def get_fresh_random_desktop_user_agent():
        """
        Get a fresh random desktop user agent with enhanced entropy.
        Uses multiple entropy sources for better randomization.

        Returns:
            str: A fresh random desktop user agent string
        """
        import time
        import os

        desktop_agents = EnhancedSeleniumBaseDriver.get_desktop_user_agents()

        # Use multiple entropy sources for better randomization
        entropy_sources = [
            int(time.time() * 1000000) % 1000000,  # Microsecond timestamp
            os.getpid(),  # Process ID
            hash(str(time.time())) % 1000000,  # Hash of current time
            random.randint(0, 1000000)  # Additional random number
        ]

        # Combine entropy sources
        combined_entropy = sum(entropy_sources) % len(desktop_agents)

        # Select user agent based on combined entropy
        selected_ua = desktop_agents[combined_entropy]

        return selected_ua
    """
    Enhanced SeleniumBase Driver with advanced antidetect capabilities
    Replaces the original selenium-wire Driver class with SeleniumBase
    """

    def __init__(self):
        """Initialize the enhanced SeleniumBase driver"""
        self.url = None

        # Setup logging
        logging.basicConfig(
            filename='app.log',
            level=logging.DEBUG,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        )
        self.logger = logging.getLogger(f"EnhancedDriver")


        # Initialize browser with enhanced stealth
        self.browser = self._create_enhanced_browser()
        random_string = ''.join(random.choices(string.ascii_letters, k=8))
        # Initialize behavioral simulator with profile-specific seed
        profile_seed = hash(random_string) % 10000  # Generate consistent seed from email
        self.behavioral_simulator = BehavioralSimulator(self.browser, profile_seed)



    def create_uc_mode_browser(self):
        """Create a new browser using SeleniumBase UC Mode with SB context manager for better proxy authentication"""
        try:
            self.logger.info("Creating UC Mode browser with SB context manager for enhanced proxy authentication")

            # Clean up existing browser if any
            if hasattr(self, 'browser') and self.browser:
                try:
                    # Check if this is an SB context manager (BaseCase)
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'quit'):
                        self.browser.driver.quit()
                    elif hasattr(self.browser, 'quit'):
                        self.browser.quit()
                except:
                    pass

            # Clean up existing SB context if any
            self.cleanup_sb_context()

            # Create new UC Mode browser
            self.browser = self._create_enhanced_browser(use_uc_mode_sb=True)
            random_string = ''.join(random.choices(string.ascii_letters, k=8))
            # Update behavioral simulator with new browser
            if hasattr(self, 'behavioral_simulator'):
                profile_seed = hash(random_string) % 10000
                self.behavioral_simulator = BehavioralSimulator(self.browser, profile_seed)

            self.logger.info("UC Mode browser created successfully")
            return self.browser

        except Exception as e:
            self.logger.error(f"Failed to create UC Mode browser: {str(e)}")
            raise e


    def _cleanup_chrome_processes(self):
        """Clean up any existing Chrome processes before starting new ones"""
        try:
            import psutil

            # Kill any existing Chrome processes
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        # Check if it's our Chrome process by looking at command line
                        cmdline = proc.info.get('cmdline', [])
                        if any('--user-data-dir' in arg for arg in cmdline if arg):
                            self.logger.info(f"Terminating existing Chrome process: {proc.info['pid']}")
                            proc.terminate()
                            proc.wait(timeout=5)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue

        except Exception as e:
            self.logger.warning(f"Error cleaning up Chrome processes: {e}")

        # Also kill chromedriver processes
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    if proc.info['name'] and 'chromedriver' in proc.info['name'].lower():
                        self.logger.info(f"Terminating chromedriver process: {proc.info['pid']}")
                        proc.terminate()
                        proc.wait(timeout=5)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.TimeoutExpired):
                    continue
        except Exception as e:
            self.logger.warning(f"Error cleaning up chromedriver processes: {e}")





    def _verify_chrome_flags(self, options):
        """Verify that problematic Chrome flags are not being used"""
        try:
            # Get all arguments from Chrome options
            all_args = []
            if hasattr(options, 'arguments'):
                all_args.extend(options.arguments)

            # Check for problematic flags
            problematic_flags = [
                '--disable-web-security',
                '--reduce-security-for-testing',
                '--ignore-certificate-errors'  # Also deprecated
            ]

            found_problematic = []
            for arg in all_args:
                for flag in problematic_flags:
                    if flag in arg:
                        found_problematic.append(arg)

            if found_problematic:
                self.logger.error(f"Found problematic Chrome flags: {found_problematic}")
                # Remove problematic flags
                for flag in found_problematic:
                    if flag in options.arguments:
                        options.arguments.remove(flag)
                        self.logger.info(f"Removed problematic flag: {flag}")
            else:
                self.logger.info("Chrome flags verification passed - no problematic flags found")

        except Exception as e:
            self.logger.warning(f"Error verifying Chrome flags: {e}")



    def _remove_problematic_flags_from_driver(self, driver):
        """Remove problematic flags from an already created driver"""
        try:
            # Try to access the Chrome options from the driver
            if hasattr(driver, 'driver') and hasattr(driver.driver, 'capabilities'):
                caps = driver.driver.capabilities
                if 'chrome' in caps and 'chromedriverVersion' in caps['chrome']:
                    self.logger.info("Checking Chrome driver capabilities for problematic flags")

            # Try to modify Chrome options if accessible
            if hasattr(driver, 'options'):
                self._verify_chrome_flags(driver.options)

            # Use CDP to override any problematic settings
            if hasattr(driver, 'execute_cdp_cmd'):
                try:
                    # Override security settings via CDP
                    driver.execute_cdp_cmd('Security.setIgnoreCertificateErrors', {'ignore': True})
                    self.logger.info("Applied security settings via CDP instead of problematic flags")
                except Exception as e:
                    self.logger.debug(f"CDP security override not available: {e}")

        except Exception as e:
            self.logger.warning(f"Error removing problematic flags from driver: {e}")



    def _create_enhanced_browser(self, use_uc_mode_sb=True):
        """Create enhanced browser with SeleniumBase or fallback to standard selenium"""
        try:
            self.logger.info(f"Creating enhanced browser")

            # Clean up any existing Chrome processes and cached flags
            self._cleanup_chrome_processes()

            if SELENIUMBASE_AVAILABLE and SBDriver:
                if use_uc_mode_sb:
                    # Try the new SB UC Mode approach for better proxy authentication
                    try:
                        return self._create_uc_mode_browser_with_sb()
                    except Exception as uc_error:
                        self.logger.warning(f"SB UC Mode failed, falling back to standard SeleniumBase: {uc_error}")
                        return self._create_seleniumbase_browser()
                else:
                    return
            else:
                return

        except Exception as e:
            self.logger.error(f"Failed to create enhanced browser: {str(e)}")
            # Fallback to standard browser if SeleniumBase fails
            if SELENIUMBASE_AVAILABLE:
                self.logger.info("Falling back to standard selenium browser")
                return
            raise e



    def _create_seleniumbase_browser(self):
        """Create SeleniumBase browser with CDP Mode and proxy extension for enhanced stealth"""
        self.logger.info("Using SeleniumBase driver with CDP Mode and proxy extension for maximum stealth")

        # No complex proxy configuration needed - using simple extension loading

        # Setup both proxy and CSP extensions
        proxy_extension_path = None
        csp_extension_path = None
        try:
            proxy_extension_path, csp_extension_path = self._setup_extensions()
        except Exception as e:
            self.logger.warning(f"Failed to setup extensions, falling back to standard proxy: {e}")

        # Get fresh random Chrome ********* user agent with enhanced randomization
        random_user_agent = self.get_fresh_random_desktop_user_agent()
        self.logger.info(f"🎲 Selected fresh random Chrome ********* user agent: {random_user_agent[:80]}...")
        self.logger.info(f"🔍 Full user agent: {random_user_agent}")

        # Create SeleniumBase driver with UC Mode (required for CDP Mode)
        # Use minimal arguments - let CDP Mode handle stealth
        driver_kwargs = {
            'browser': 'chrome',
            'headless': False,
            'uc': True,  # Enable UC Mode (required for CDP Mode)
            'agent': random_user_agent,  # Set custom user agent (correct SeleniumBase parameter)
            #'user_data_dir': self.profile_config['profile_path'],
            'disable_csp': True,  # Disable Content Security Policy
            'disable_ws': True,   # Disable WebSocket connections that might interfere
            # Note: disable_web_security is not a valid SBDriver parameter
            # We'll handle this via Chrome options instead
        }

        # Add extensions if available - SeleniumBase can only handle one extension_dir
        # We'll use Chrome arguments to load multiple extensions instead
        extensions_to_load = []
        if proxy_extension_path:
            extensions_to_load.append(proxy_extension_path)
            self.logger.info("Will load proxy extension via Chrome arguments")
        if csp_extension_path:
            extensions_to_load.append(csp_extension_path)
            self.logger.info("Will load CSP extension via Chrome arguments")

        # Configure proxy: Use extension if available, otherwise fall back to standard proxy
        if proxy_extension_path:
            # Using proxy extension - no need for standard proxy configuration
            self.logger.info("Using proxy extension instead of standard proxy configuration")

        # Only add essential Chrome arguments (profile and extension related)
        # Let UC/CDP Mode handle stealth automatically
        chrome_args = []

        # Essential profile argument
        #chrome_args.append(f'--user-data-dir={self.profile_config["profile_path"]}')

        # Add extension support - always enable for proxy extension or other extensions
        chrome_args.extend([
            '--extensions-on-chrome-urls',
            '--allow-running-insecure-content',
        ])

        # Add developer mode and extension loading if using extensions
        if extensions_to_load:
            # Join multiple extension paths with comma for Chrome
            extensions_arg = ','.join(extensions_to_load)
            chrome_args.extend([
                '--enable-extension-activity-logging',
                '--enable-logging',
                '--log-level=0',  # Enable detailed logging for debugging
                #f'--load-extension={extensions_arg}',  # Load both proxy and CSP extensions
                #'--enable-extensions',  # Enable extensions
            ])
            self.logger.info(f"Added developer mode and extension loading for extensions: {extensions_to_load}")

        # Add explicit security flags to prevent --disable-web-security
        chrome_args.extend([
            '--enable-web-security',  # Explicitly enable web security
            '--disable-features=VizDisplayCompositor',  # Alternative to problematic flags
        ])

        # Set minimal Chrome arguments (let UC/CDP Mode handle the rest)
        if chrome_args:
            driver_kwargs['chromium_arg'] = ','.join(chrome_args)
            self.logger.info(f"Added essential Chrome args: {len(chrome_args)} args")

        # User agent is already set via 'agent' parameter in driver_kwargs
        # No need to add --user-agent Chrome argument to avoid conflicts

        # Create the driver with UC Mode
        driver = SBDriver(**driver_kwargs)
        self.logger.info("SeleniumBase UC Mode driver created successfully")

        # Explicitly remove problematic flags from the created driver
        #self._remove_problematic_flags_from_driver(driver)

        # Set extended timeouts for proxy connections
        try:
            driver.set_page_load_timeout(240)  # Increased timeout for proxy connections
            driver.implicitly_wait(60)  # Increased implicit wait
            self.logger.info("Extended timeouts set for proxy connections")
        except Exception as e:
            self.logger.warning(f"Could not set extended timeouts: {str(e)}")

        # Note: CDP Mode will be activated when needed via activate_cdp_mode() method
        # This provides better stealth than manual Chrome arguments
        self.logger.info("UC Mode driver ready - CDP Mode can be activated when needed for maximum stealth")

        # Store browser reference
        self.browser = driver

        return driver


    def _create_uc_mode_browser_with_sb(self):
        """Create SeleniumBase browser using SB context manager with UC Mode for better proxy authentication"""
        try:
            from seleniumbase import SB

            self.logger.info("Using SeleniumBase SB context manager with UC Mode for enhanced proxy authentication")

            # No complex proxy configuration needed - using simple extension loading

            # Setup extensions
            proxy_extension_path , csp_extension_path, webrtc_extension_path= self._setup_extensions()
            extension_dir = True



            # No proxy string preparation needed - using extension-based proxy

            # Get fresh random Chrome ********* user agent with enhanced randomization
            random_user_agent = self.get_fresh_random_desktop_user_agent()
            self.logger.info(f"🎲 Selected fresh random Chrome ********* user agent (SB): {random_user_agent[:80]}...")
            self.logger.info(f"🔍 Full user agent (SB): {random_user_agent}")

            # SB context manager parameters
            sb_kwargs = {
                'uc': True,  # Enable UC Mode
                'xvfb': True,  # Use virtual display (try this first)
                'ad_block_on': True,
                'agent': random_user_agent,  # Set custom user agent (correct SeleniumBase parameter)
                #'user_data_dir': self.profile_config['profile_path'],
                'extension_dir': f"{proxy_extension_path},{csp_extension_path},{webrtc_extension_path}"
            }

            # Add extension directory if available
            if extension_dir:
                sb_kwargs['extension_dir'] = f"{proxy_extension_path},{csp_extension_path},{webrtc_extension_path}"
                self.logger.info(f"Added extension directory to SB: {extension_dir}")

            # Using extension-based proxy only
            if extension_dir:
                self.logger.info("Using extension-based proxy")

            self.logger.info(f"Creating SB context manager with parameters: {list(sb_kwargs.keys())}")

            # Store SB context manager for later use
            self.sb_context = SB(**sb_kwargs)

            # Enter the context and get the driver
            self.sb_driver = self.sb_context.__enter__()

            self.logger.info("SB context manager created successfully with UC Mode")


            # Set extended timeouts using the raw driver
            try:
                self.sb_driver.driver.set_page_load_timeout(180)
                self.sb_driver.driver.implicitly_wait(30)
                self.logger.info("Extended timeouts set for SB UC Mode driver")
            except Exception as e:
                self.logger.warning(f"Could not set extended timeouts: {str(e)}")
            try:
                # Choose and apply random window size directly with sb_driver
                self.window_size_choice, self.window_width, self.window_height = self._choose_random_window_size_for_sb_driver()

            except Exception as e:
                self.logger.warning(f"Could not set random window size: {e}")
                # Set default values
                self.window_size_choice = 'custom'
                self.window_width = 1366
                self.window_height = 768

            return self.sb_driver

        except Exception as e:
            self.logger.error(f"Failed to create SB UC Mode browser: {str(e)}")
            # Try fallback with headless2 instead of xvfb
            return self._create_uc_mode_browser_with_sb_fallback()

    def _create_uc_mode_browser_with_sb_fallback(self):
        """Fallback SB UC Mode browser creation with headless2 instead of xvfb"""
        try:
            from seleniumbase import SB

            self.logger.info("Trying SB UC Mode fallback with headless2 instead of xvfb")

            # No complex proxy configuration needed - using simple extension loading

            # Setup extensions (same as above)
            proxy_extension_path = None
            csp_extension_path = None
            extension_dir = None

            try:
                proxy_extension_path, csp_extension_path = self._setup_extensions()
                if proxy_extension_path and csp_extension_path:
                    import tempfile
                    extension_dir = tempfile.mkdtemp(prefix="sb_extensions_fallback_")

                    import shutil
                    proxy_dest = os.path.join(extension_dir, "proxy_extension")
                    csp_dest = os.path.join(extension_dir, "csp_extension")

                    shutil.copytree(proxy_extension_path, proxy_dest)
                    shutil.copytree(csp_extension_path, csp_dest)

                    self.logger.info(f"Extensions prepared for SB fallback at: {extension_dir}")

            except Exception as e:
                self.logger.warning(f"Failed to setup extensions for SB fallback: {e}")

            # No proxy string preparation needed - using extension-based proxy

            # Get fresh random Chrome ********* user agent for fallback with enhanced randomization
            random_user_agent = self.get_fresh_random_desktop_user_agent()
            self.logger.info(f"🎲 Selected fresh random Chrome ********* user agent (Fallback): {random_user_agent[:80]}...")
            self.logger.info(f"🔍 Full user agent (Fallback): {random_user_agent}")

            # SB fallback parameters
            sb_kwargs = {
                'uc': True,  # Enable UC Mode
                'headless2': True,  # Use headless2 instead of xvfb
                'agent': random_user_agent,  # Set custom user agent (correct SeleniumBase parameter)
                #'user_data_dir': self.profile_config['profile_path'],
            }

            # Add extension directory if available
            if extension_dir:
                sb_kwargs['extension_dir'] = extension_dir

            # Using extension-based proxy only

            self.logger.info(f"Creating SB fallback with parameters: {list(sb_kwargs.keys())}")

            # Store SB context manager for later use
            self.sb_context = SB(**sb_kwargs)

            # Enter the context and get the driver
            self.sb_driver = self.sb_context.__enter__()

            self.logger.info("SB fallback context manager created successfully with UC Mode and headless2")

            # Set extended timeouts using the raw driver
            try:
                self.sb_driver.driver.set_page_load_timeout(180)
                self.sb_driver.driver.implicitly_wait(30)
                self.logger.info("Extended timeouts set for SB UC Mode fallback driver")
            except Exception as e:
                self.logger.warning(f"Could not set extended timeouts: {str(e)}")

            try:
                # Choose and apply random window size directly with sb_driver
                self.window_size_choice, self.window_width, self.window_height = self._choose_random_window_size_for_sb_driver()

            except Exception as e:
                self.logger.warning(f"Could not set random window size: {e}")
                # Set default values
                self.window_size_choice = 'custom'
                self.window_width = 1366
                self.window_height = 768

            return self.sb_driver

        except Exception as e:
            self.logger.error(f"SB UC Mode fallback also failed: {str(e)}")
            raise e

    def uc_open_with_reconnect(self, url, reconnect_time=5):
        """Use SeleniumBase UC Mode navigation with reconnect for enhanced stealth"""
        try:
            if hasattr(self, 'sb_driver') and self.sb_driver:
                # Use SB context manager driver
                if hasattr(self.sb_driver, 'uc_open_with_reconnect'):
                    self.sb_driver.uc_open_with_reconnect(url, reconnect_time)
                    self.logger.info(f"SB UC Mode navigation with reconnect to: {url}")
                    return True
                else:
                    # Fallback to regular navigation
                    self.sb_driver.get(url)
                    self.logger.info(f"SB regular navigation to: {url}")
                    return True
            elif hasattr(self, 'browser') and self.browser:
                # Use regular SeleniumBase driver
                if hasattr(self.browser, 'uc_open_with_reconnect'):
                    self.browser.uc_open_with_reconnect(url, reconnect_time)
                    self.logger.info(f"UC Mode navigation with reconnect to: {url}")
                    return True
                else:
                    # Fallback to regular navigation using appropriate method for SB context manager or regular WebDriver
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'get'):
                        self.browser.driver.get(url)
                    elif hasattr(self.browser, 'get'):
                        self.browser.get(url)
                    self.logger.info(f"Regular navigation to: {url}")
                    return True
            else:
                self.logger.error("No browser available for UC Mode navigation")
                return False

        except Exception as e:
            self.logger.error(f"UC Mode navigation failed: {str(e)}")
            return False

    def cleanup_sb_context(self):
        """Clean up SB context manager when done"""
        try:
            if hasattr(self, 'sb_context') and self.sb_context:
                self.sb_context.__exit__(None, None, None)
                self.logger.info("SB context manager cleaned up")
        except Exception as e:
            self.logger.warning(f"Error cleaning up SB context: {str(e)}")

    def activate_cdp_mode(self, url=None):
        """Activate CDP Mode for maximum stealth - call this when you need enhanced bot detection bypass"""
        if hasattr(self, 'browser') and self.browser:
            try:
                # Method 1: Try SeleniumBase's built-in activate_cdp_mode
                if hasattr(self.browser, 'activate_cdp_mode'):
                    if url:
                        self.browser.activate_cdp_mode(url)
                        self.logger.info(f"CDP Mode activated with URL: {url}")
                    else:
                        self.browser.activate_cdp_mode()
                        self.logger.info("CDP Mode activated")
                    return True

                # Method 2: Try using uc_open_with_reconnect for stealth navigation
                elif hasattr(self.browser, 'uc_open_with_reconnect') and url:
                    self.browser.uc_open_with_reconnect(url, reconnect_time=4)
                    self.logger.info(f"UC Mode stealth navigation to: {url}")
                    return True

                # Method 3: Try disconnect/reconnect pattern for stealth
                elif hasattr(self.browser, 'disconnect') and hasattr(self.browser, 'reconnect'):
                    self.browser.disconnect()
                    self.logger.info("WebDriver disconnected for stealth mode")
                    if url:
                        # Use JavaScript to navigate while disconnected using appropriate method for SB context manager or regular WebDriver
                        if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'execute_script'):
                            self.browser.driver.execute_script(f'window.location.href = "{url}";')
                        elif hasattr(self.browser, 'execute_script'):
                            self.browser.execute_script(f'window.location.href = "{url}";')
                        import time
                        time.sleep(3)
                    self.browser.reconnect(timeout=5)
                    self.logger.info("Stealth mode activated with disconnect/reconnect")
                    return True

                else:
                    self.logger.warning("CDP Mode methods not available - UC Mode is still active for stealth")
                    if url:
                        # Fallback to regular navigation with UC Mode using appropriate method for SB context manager or regular WebDriver
                        if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'get'):
                            self.browser.driver.get(url)
                        elif hasattr(self.browser, 'get'):
                            self.browser.get(url)
                        self.logger.info(f"Navigated with UC Mode stealth to: {url}")
                    return True  # UC Mode is still providing stealth

            except Exception as e:
                self.logger.error(f"Failed to activate CDP Mode: {str(e)}")
                # Fallback to UC Mode navigation
                if url:
                    try:
                        # Fallback navigation using appropriate method for SB context manager or regular WebDriver
                        if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'get'):
                            self.browser.driver.get(url)
                        elif hasattr(self.browser, 'get'):
                            self.browser.get(url)
                        self.logger.info(f"Fallback: Navigated with UC Mode to: {url}")
                        return True
                    except Exception as fallback_error:
                        self.logger.error(f"Fallback navigation failed: {fallback_error}")
                return False
        else:
            self.logger.error("No browser available to activate CDP Mode")
            return False



    def _setup_extensions(self) -> tuple:
        """Setup proxy and CSP extensions using simple Chrome extension loading"""
        try:
            # Setup simple proxy extension loading
            proxy_extension_path = os.path.join(
                os.path.dirname(__file__),
                'extensions',
                'shared_proxy_extension'
            )

            # Verify proxy extension file exists
            if not os.path.exists(proxy_extension_path):
                self.logger.warning(f"Proxy extension file not found: {proxy_extension_path}")
                proxy_extension_path = None
            else:
                self.logger.info(f"Proxy extension ready: {proxy_extension_path}")

            # Setup CSP extension (always enabled for automation)
            csp_extension_path = self._setup_csp_extension()
            self.logger.info(f"CSP extension prepared at: {csp_extension_path}")

            # Setup webrtc extension (always enabled for automation)
            webrtc_extension_path = self._setup_webrtc_extension()
            self.logger.info(f"Webrtc extension prepared at: {webrtc_extension_path}")

            # Setup webrtc extension (always enabled for automation)
            #adblock_extension_path = self._setup_adblock_extension()
            #self.logger.info(f"adblock extension prepared at: {adblock_extension_path}")

            return proxy_extension_path, csp_extension_path, webrtc_extension_path

        except Exception as e:
            self.logger.error(f"Error setting up extensions: {str(e)}")
            raise


    def _setup_csp_extension(self) -> str:
        """Setup standalone CSP extension for SeleniumBase"""
        try:
            import os

            # Path to the standalone CSP extension
            csp_extension_path = os.path.join(
                os.path.dirname(__file__),
                'extensions',
                'disable_csp'
            )

            if not os.path.exists(csp_extension_path):
                raise Exception(f"CSP extension not found at: {csp_extension_path}")

            return csp_extension_path

        except Exception as e:
            self.logger.error(f"Error setting up CSP extension: {str(e)}")
            raise


    def _setup_webrtc_extension(self) -> str:
        """Setup standalone webrtc extension for SeleniumBase"""
        try:
            import os

            # Path to the standalone CSP extension
            webrtc_extension_path = os.path.join(
                os.path.dirname(__file__),
                'extensions',
                'webrtc'
            )

            if not os.path.exists(webrtc_extension_path):
                raise Exception(f"webrtc extension not found at: {webrtc_extension_path}")

            return webrtc_extension_path

        except Exception as e:
            self.logger.error(f"Error setting up webrtc extension: {str(e)}")
            raise


    def _setup_adblock_extension(self) -> str:
        """Setup standalone webrtc extension for SeleniumBase"""
        try:
            import os

            # Path to the standalone CSP extension
            adblock_extension_path = os.path.join(
                os.path.dirname(__file__),
                'extensions',
                'adblock'
            )

            if not os.path.exists(adblock_extension_path):
                raise Exception(f"adblock extension not found at: {adblock_extension_path}")

            return adblock_extension_path

        except Exception as e:
            self.logger.error(f"Error setting up adblock extension: {str(e)}")
            raise


    def _get_chrome_options(self):
        """Get Chrome options for enhanced stealth with profile-specific configurations"""
        from selenium.webdriver.chrome.options import Options

        options = Options()

        # Remove manual user-agent setting - let SeleniumBase UC mode handle it automatically
        # This ensures better compatibility and natural user-agent assignment
        self.logger.info("Allowing SeleniumBase to automatically handle user-agent for better stealth")

        # Control startup behavior to prevent multiple tabs
        options.add_argument('--no-default-browser-check')
        options.add_argument('--no-first-run')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-extensions-file-access-check')
        options.add_argument('--disable-extensions-http-throttling')

        # Set blank page as startup to prevent diagnostic pages
        options.add_argument('--homepage=about:blank')
        options.add_argument('--new-window')

        # Disable problematic features that can trigger detection pages
        options.add_argument('--disable-component-extensions-with-background-pages')
        options.add_argument('--disable-background-networking')
        options.add_argument('--disable-sync')

        # Add profile directory from profile configuration
        #options.add_argument(f'--user-data-dir={self.profile_config["profile_path"]}')

        # Get profile-specific fingerprint for other settings
        fingerprint = {}

        # Add profile-specific screen resolution
        screen_resolution = fingerprint.get('screen_resolution', {'width': 1920, 'height': 1080})
        options.add_argument(f'--window-size={screen_resolution["width"]},{screen_resolution["height"]}')

        # Add profile-specific language settings
        language = fingerprint.get('language', 'fr-FR,fr;q=0.9,en;q=0.8')
        options.add_argument(f'--lang={language.split(",")[0]}')
        options.add_experimental_option('prefs', {
            'intl.accept_languages': language
        })

        # Phase 3.2: Network-Level Stealth - TLS Fingerprinting Randomization
        tls_options = self._get_tls_fingerprinting_options()
        for tls_arg in tls_options:
            options.add_argument(tls_arg)

        # Phase 3.2: Network-Level Stealth - HTTP/2 Fingerprinting Protection
        http2_options = self._get_http2_fingerprinting_options()
        for http2_arg in http2_options:
            options.add_argument(http2_arg)

        # Phase 3.2: Network-Level Stealth - DNS Leak Protection
        dns_options = self._get_dns_protection_options()
        for dns_arg in dns_options:
            options.add_argument(dns_arg)

        # Phase 3.2: Network-Level Stealth - Enhanced WebRTC Leak Protection
        webrtc_options = self._get_webrtc_protection_options()
        for webrtc_arg in webrtc_options:
            options.add_argument(webrtc_arg)

        # Enhanced stealth arguments
        args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-blink-features=AutomationControlled',
            # Extensions enabled for proxy functionality
            '--enable-extensions',
            '--extensions-on-chrome-urls',
            '--disable-default-apps',
            '--disable-sync',  # Allow sync to enable automatically
            '--disable-translate',
            '--hide-scrollbars',
            '--mute-audio',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-logging',
            '--disable-gpu-logging',
            '--disable-software-rasterizer',
            '--log-level=3',
            '--silent',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-client-side-phishing-detection',
            '--disable-crash-reporter',
            '--disable-oopr-debug-crash-dump',
            '--no-crash-upload',
            '--disable-low-res-tiling',
            '--disable-background-networking',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI,BlinkGenPropertyTrees',
            '--disable-ipc-flooding-protection',
            '--disable-hang-monitor',
            '--disable-client-side-phishing-detection',
            '--disable-popup-blocking',
            '--disable-prompt-on-repost',
            '--disable-domain-reliability',
            '--disable-component-update',
            '--disable-background-downloads',
            '--disable-add-to-shelf',
            '--disable-datasaver-prompt',
            '--disable-device-discovery-notifications',
            '--disable-infobars',
            '--disable-notifications',
            '--disable-save-password-bubble',
            '--disable-single-click-autofill',
            '--disable-voice-input',
            '--disable-wake-on-wifi',
            # REMOVED: '--disable-web-security',  # This breaks cookies
            '--allow-running-insecure-content',
            '--disable-web-resources',
            # REMOVED: '--reduce-security-for-testing',  # This can break cookies too
            '--allow-cross-origin-auth-prompt',
            '--disable-features=VizDisplayCompositor',
            # Enhanced SSL and HTTPS proxy options
            # Note: --ignore-certificate-errors is deprecated in newer Chrome versions
            # Using modern alternatives instead
            '--ignore-ssl-errors',
            '--disable-certificate-transparency',
            '--allow-insecure-localhost',
            # REMOVED: '--disable-web-security',  # This breaks cookies
            '--allow-running-insecure-content',
            # REMOVED: '--reduce-security-for-testing',  # This can break cookies too
            # Removed problematic host resolver rules that can cause diagnostic pages
            '--disable-features=VizDisplayCompositor,TranslateUI',
            '--disable-background-networking',
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--aggressive-cache-discard',
            '--disable-ipc-flooding-protection',
            '--no-service-autorun',
            '--password-store=basic',
            '--use-mock-keychain',
            # Additional SSL/TLS options for proxy compatibility
            '--ssl-version-fallback-min=tls1',
            '--disable-features=VizDisplayCompositor',
            '--disable-gpu',
            '--no-first-run',
            '--disable-default-apps',
            # REMOVED: '--disable-sync',  # Allow sync to enable automatically
            '--disable-translate',
            # Enhanced proxy and SSL options
            '--ignore-urlfetcher-cert-requests',
            '--disable-background-downloads',
            '--disable-component-update',
            '--disable-domain-reliability',
            '--disable-client-side-phishing-detection',
            '--disable-default-apps',
            '--disable-hang-monitor',
            '--disable-prompt-on-repost',
            '--disable-popup-blocking',
            '--allow-cross-origin-auth-prompt',
            '--disable-features=TranslateUI,BlinkGenPropertyTrees',
            # Network timeout and retry options
            '--network-service-logging-level=0',
            # Removed duplicate user agent (already set above)
            # Removed remote debugging port (can cause diagnostic pages)
        ]

        # Add all arguments to options
        for arg in args:
            options.add_argument(arg)

        # Additional startup control to prevent multiple tabs
        options.add_experimental_option('useAutomationExtension', False)
        options.add_experimental_option("excludeSwitches", ["enable-automation"])

        # Set preferences to control startup behavior
        prefs = {
            # Disable multiple startup tabs
            "browser.startup.homepage": "about:blank",
            "browser.startup.page": 0,  # 0 = blank page, 1 = home page, 3 = last session
            "session.restore_on_startup": 0,  # Don't restore previous session

            # Disable various Chrome features that might open tabs
            "browser.show_home_button": False,
            "browser.startup.homepage_is_newtabpage": False,
            "distribution.import_bookmarks": False,
            "distribution.import_history": False,
            "distribution.import_search_engine": False,
            "distribution.suppress_first_run_bubble": True,
            "distribution.suppress_first_run_default_browser_prompt": True,

            # Disable background apps and services
            "background_mode.enabled": False,
            "hardware_acceleration_mode.enabled": False,

            # Enable Chrome Sync automatically
            "sync.suppress_start": False,  # Don't suppress sync start
            "sync.requested": True,  # Request sync to be enabled
            "sync.keep_everything_synced": True,  # Sync everything
            "sync.autofill_enabled": True,  # Enable autofill sync
            "sync.bookmarks_enabled": True,  # Enable bookmarks sync
            "sync.passwords_enabled": True,  # Enable passwords sync
            "sync.preferences_enabled": True,  # Enable preferences sync
            "sync.tabs_enabled": True,  # Enable tabs sync
            "sync.themes_enabled": True,  # Enable themes sync
            "sync.extensions_enabled": True,  # Enable extensions sync
            "sync.apps_enabled": True,  # Enable apps sync
        }

        # Merge with existing prefs
        existing_prefs = options.experimental_options.get('prefs', {})
        existing_prefs.update(prefs)
        options.add_experimental_option('prefs', existing_prefs)

        # Verify that no problematic flags are being used
        self._verify_chrome_flags(options)

        return options

    # ============================================================================
    # 2FA (Two-Factor Authentication) Methods
    # ============================================================================

    def store_2fa_secret(self, service_name: str, secret: str, issuer: Optional[str] = None,
                        account_name: Optional[str] = None) -> bool:
        """
        Store a 2FA secret for this profile

        Args:
            service_name: Name of the service (e.g., 'google', 'github')
            secret: Base32 encoded secret key
            issuer: Optional issuer name
            account_name: Optional account name

        Returns:
            bool: True if stored successfully
        """
        if not self.twofa_manager:
            self.logger.warning("2FA manager not available")
            return False

        profile_identifier = f"{self.email.split('@')[0]}_{self.index}"
        return self.twofa_manager.store_2fa_secret(
            profile_identifier=profile_identifier,
            service_name=service_name,
            secret=secret,
            issuer=issuer,
            account_name=account_name or self.email
        )

    def get_2fa_code(self, service_name: str) -> Optional[str]:
        """
        Generate current 2FA code for a service

        Args:
            service_name: Name of the service

        Returns:
            str: Current 6-digit 2FA code, or None if not found
        """
        if not self.twofa_manager:
            self.logger.warning("2FA manager not available")
            return None

        profile_identifier = f"{self.email.split('@')[0]}_{self.index}"
        return self.twofa_manager.get_2fa_code(profile_identifier, service_name)

    def scan_and_store_2fa_qr(self, image_path: str, service_name: Optional[str] = None) -> bool:
        """
        Scan QR code and store 2FA secret for this profile

        Args:
            image_path: Path to QR code image
            service_name: Optional service name override

        Returns:
            bool: True if stored successfully
        """
        if not self.twofa_manager:
            self.logger.warning("2FA manager not available")
            return False

        profile_identifier = f"{self.email.split('@')[0]}_{self.index}"
        return self.twofa_manager.store_from_qr_code(profile_identifier, image_path, service_name)

    def scan_2fa_qr_from_screenshot(self, service_name: Optional[str] = None) -> bool:
        """
        Take screenshot, scan for QR code, and store 2FA secret

        Args:
            service_name: Optional service name override

        Returns:
            bool: True if QR code found and stored successfully
        """
        if not self.twofa_manager:
            self.logger.warning("2FA manager not available")
            return False

        try:
            # Take screenshot
            screenshot_data = self.browser.get_screenshot_as_png()

            # Scan QR code from screenshot
            profile_identifier = f"{self.email.split('@')[0]}_{self.index}"
            qr_info = self.twofa_manager.scan_qr_code_from_screenshot(screenshot_data)

            if not qr_info:
                self.logger.warning("No QR code found in screenshot")
                return False

            # Store the secret
            final_service_name = service_name or qr_info['issuer'] or 'unknown_service'
            return self.twofa_manager.store_2fa_secret(
                profile_identifier=profile_identifier,
                service_name=final_service_name,
                secret=qr_info['secret'],
                issuer=qr_info['issuer'],
                account_name=qr_info['account_name'] or self.email
            )

        except Exception as e:
            self.logger.error(f"Error scanning QR code from screenshot: {e}")
            return False

    def get_2fa_services(self) -> List[str]:
        """
        Get list of services with 2FA secrets for this profile

        Returns:
            list: List of service names
        """
        if not self.twofa_manager:
            return []

        profile_identifier = f"{self.email.split('@')[0]}_{self.index}"
        return self.twofa_manager.get_profile_services(profile_identifier)

    def remove_2fa_secret(self, service_name: str) -> bool:
        """
        Remove a 2FA secret for a service

        Args:
            service_name: Name of the service to remove

        Returns:
            bool: True if removed successfully
        """
        if not self.twofa_manager:
            self.logger.warning("2FA manager not available")
            return False

        profile_identifier = f"{self.email.split('@')[0]}_{self.index}"
        return self.twofa_manager.remove_2fa_secret(profile_identifier, service_name)

    def generate_2fa_backup_codes(self, service_name: str, count: int = 10) -> List[str]:
        """
        Generate backup codes for a 2FA service

        Args:
            service_name: Name of the service
            count: Number of backup codes to generate

        Returns:
            list: Generated backup codes
        """
        if not self.twofa_manager:
            self.logger.warning("2FA manager not available")
            return []

        profile_identifier = f"{self.email.split('@')[0]}_{self.index}"
        return self.twofa_manager.generate_backup_codes(profile_identifier, service_name, count)

    def _get_tls_fingerprinting_options(self):
        """Get TLS fingerprinting randomization options for Phase 3.2"""
        import random

        # Get profile-specific seed for consistent TLS fingerprinting
        fingerprint =  {}
        tls_seed = fingerprint.get('canvas_seed', 12345) + 5000  # Use canvas seed + offset for TLS

        # Set random seed for consistent TLS options per profile
        random.seed(tls_seed)

        tls_options = []

        # TLS version randomization
        tls_versions = ['--ssl-version-min=tls1.2', '--ssl-version-min=tls1.3']
        tls_options.append(random.choice(tls_versions))

        # Cipher suite randomization
        cipher_suites = [
            '--cipher-suite-blacklist=0x0004,0x0005,0x0017,0x0018,0x0019,0x001a',
            '--cipher-suite-blacklist=0x002f,0x0035,0x003c,0x003d,0x0041,0x0084',
            '--cipher-suite-blacklist=0x0088,0x0087,0x009c,0x009d,0x009e,0x009f'
        ]
        if random.random() > 0.5:
            tls_options.append(random.choice(cipher_suites))

        # TLS extension randomization
        tls_extensions = [
            '--disable-tls-false-start',
            '--enable-tls-false-start',
            '--disable-tls13-early-data'
        ]
        tls_options.append(random.choice(tls_extensions))

        # Certificate verification options (using modern alternatives)
        cert_options = [
            # Note: --ignore-certificate-errors is deprecated in newer Chrome versions
            '--ignore-ssl-errors',
            '--disable-certificate-transparency',
            '--allow-insecure-localhost'
        ]
        if random.random() > 0.3:
            tls_options.append(random.choice(cert_options))

        # Reset random seed
        random.seed()

        self.logger.info(f"Applied TLS fingerprinting options: {len(tls_options)} options")
        return tls_options

    def _get_http2_fingerprinting_options(self):
        """Get HTTP/2 fingerprinting protection options for Phase 3.2"""
        import random

        # Get profile-specific seed for consistent HTTP/2 fingerprinting
        fingerprint = {}

        http2_seed = fingerprint.get('canvas_seed', 12345) + 6000  # Use canvas seed + offset for HTTP/2

        # Set random seed for consistent HTTP/2 options per profile
        random.seed(http2_seed)

        http2_options = []

        # HTTP/2 settings randomization
        if random.random() > 0.5:
            http2_options.append('--disable-http2')
        else:
            # HTTP/2 window size randomization
            window_sizes = ['65536', '131072', '262144', '524288']
            window_size = random.choice(window_sizes)
            http2_options.append(f'--http2-settings=SETTINGS_INITIAL_WINDOW_SIZE={window_size}')

            # HTTP/2 max frame size randomization
            frame_sizes = ['16384', '32768', '65536']
            frame_size = random.choice(frame_sizes)
            http2_options.append(f'--http2-settings=SETTINGS_MAX_FRAME_SIZE={frame_size}')

        # HTTP/2 priority randomization
        priority_options = [
            '--disable-http2-priority',
            '--enable-http2-priority'
        ]
        if random.random() > 0.4:
            http2_options.append(random.choice(priority_options))

        # QUIC protocol randomization
        quic_options = [
            '--disable-quic',
            '--enable-quic',
            '--quic-version=h3-29',
            '--quic-version=h3-27'
        ]
        if random.random() > 0.6:
            http2_options.append(random.choice(quic_options))

        # Reset random seed
        random.seed()

        self.logger.info(f"Applied HTTP/2 fingerprinting options: {len(http2_options)} options")
        return http2_options

    def _get_dns_protection_options(self):
        """Get DNS leak protection options for Phase 3.2"""
        import random

        # Get profile-specific seed for consistent DNS settings
        fingerprint = {}
        dns_seed = fingerprint.get('canvas_seed', 12345) + 7000  # Use canvas seed + offset for DNS

        # Set random seed for consistent DNS options per profile
        random.seed(dns_seed)

        dns_options = []

        # DNS-over-HTTPS (DoH) configuration - FIXED to prevent navigation issues
        # Note: Removed problematic --host-resolver-rules that was causing Chrome to navigate to URLs

        # Enable DoH without problematic host resolver rules
        dns_options.append('--enable-features=DnsOverHttps')
        dns_options.append('--force-fieldtrials=DnsOverHttpsUpgrade/Enabled')

        # Use proper DNS configuration without mapping that causes navigation issues
        # Instead of mapping all hosts to DoH URLs, use standard DNS protection
        dns_options.append('--disable-background-networking')
        dns_options.append('--disable-domain-reliability')

        # DNS prefetch randomization
        if random.random() > 0.5:
            dns_options.append('--disable-dns-prefetch')
        else:
            dns_options.append('--enable-dns-prefetch')

        # DNS cache randomization
        cache_options = [
            '--aggressive-cache-discard',
            '--disable-background-networking'
        ]
        if random.random() > 0.4:
            dns_options.append(random.choice(cache_options))

        # Reset random seed
        random.seed()

        self.logger.info(f"Applied DNS protection options: {len(dns_options)} options")
        return dns_options

    def _get_webrtc_protection_options(self):
        """Get enhanced WebRTC leak protection options for Phase 3.2"""
        webrtc_options = [
            # Core WebRTC blocking
            '--disable-webrtc',
            '--disable-webrtc-multiple-routes',
            '--disable-webrtc-hw-decoding',
            '--disable-webrtc-hw-encoding',

            # WebRTC IP leak protection
            '--force-webrtc-ip-handling-policy=disable_non_proxied_udp',
            '--webrtc-ip-handling-policy=disable_non_proxied_udp',

            # Additional WebRTC security
            '--disable-webrtc-encryption',
            '--disable-webrtc-stun-origin',

            # Media device access blocking
            '--disable-media-stream',
            '--disable-camera',
            '--disable-microphone',

            # P2P connection blocking
            '--disable-p2p-api',
            '--disable-rtc-smoothness-algorithm'
        ]

        self.logger.info(f"Applied WebRTC protection options: {len(webrtc_options)} options")
        return webrtc_options



    def _apply_stealth_techniques(self, driver):
        """Apply advanced stealth techniques to avoid detection"""
        try:
            # Advanced stealth JavaScript with proper error handling
            stealth_js = """
            // Generate consistent random seed for this session
            const randomSeed = Math.floor(Math.random() * 1000000);

            // Seeded random function for consistent randomization
            function seededRandom(seed) {
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }

            // Remove webdriver property with proper error handling
            try {
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: true
                });
            } catch (e) {
                // If property already exists, try to delete it first
                try {
                    delete navigator.webdriver;
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                        configurable: true
                    });
                } catch (e2) {
                    // Fallback: override with direct assignment
                    navigator.webdriver = undefined;
                }
            }

            // Hide automation indicators
            try {
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                    configurable: true
                });
            } catch (e) {
                // Fallback for plugins
            }

            // Note: Chrome runtime is intentionally left intact
            // Removing window.chrome.runtime breaks Chrome extensions and many websites
            // This detection is expected and acceptable for most use cases
            try {
                if (!window.chrome) {
                    window.chrome = {
                        runtime: {}
                    };
                }
            } catch (e) {
                // Chrome runtime setup failed
            }

            // Fix Language/locale randomization to French
            try {
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['fr-FR', 'fr', 'en-US', 'en'],
                    configurable: true
                });
            } catch (e) {
                // Fallback for languages
            }

            try {
                Object.defineProperty(navigator, 'language', {
                    get: () => 'fr-FR',
                    configurable: true
                });
            } catch (e) {
                // Fallback for language
            }

            // Remove automation flags and advanced headless detection
            try {
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

                // Remove additional automation indicators
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Function;

                // Remove Selenium indicators
                delete window._selenium;
                delete window.__selenium_unwrapped;
                delete window.__selenium_evaluate;
                delete window.__webdriver_evaluate;
                delete window.__driver_evaluate;
                delete window.__webdriver_unwrapped;
                delete window.__driver_unwrapped;
                delete window.__fxdriver_evaluate;
                delete window.__fxdriver_unwrapped;

                // Remove PhantomJS indicators
                delete window.callPhantom;
                delete window._phantom;
                delete window.__phantom;

                // Remove headless indicators
                delete window.domAutomation;
                delete window.domAutomationController;

                // Override headless property
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: true
                });

                // Mock notification permission to avoid headless detection
                if (window.Notification) {
                    Object.defineProperty(Notification, 'permission', {
                        get: () => 'default',
                        configurable: true
                    });
                }

                // Mock permissions API
                if (navigator.permissions) {
                    const originalQuery = navigator.permissions.query;
                    navigator.permissions.query = function(parameters) {
                        return Promise.resolve({
                            state: 'granted',
                            onchange: null
                        });
                    };
                }

            } catch (e) {
                // Automation flags removal failed
            }

            // Enhanced screen resolution randomization
            const screenConfigs = [
                { width: 1920, height: 1080, availWidth: 1920, availHeight: 1040, colorDepth: 24, pixelDepth: 24 },
                { width: 1366, height: 768, availWidth: 1366, availHeight: 728, colorDepth: 24, pixelDepth: 24 },
                { width: 1536, height: 864, availWidth: 1536, availHeight: 824, colorDepth: 24, pixelDepth: 24 },
                { width: 1440, height: 900, availWidth: 1440, availHeight: 860, colorDepth: 24, pixelDepth: 24 },
                { width: 1600, height: 900, availWidth: 1600, availHeight: 860, colorDepth: 24, pixelDepth: 24 },
                { width: 2560, height: 1440, availWidth: 2560, availHeight: 1400, colorDepth: 24, pixelDepth: 24 }
            ];

            const selectedScreen = screenConfigs[Math.floor(Math.random() * screenConfigs.length)];

            Object.defineProperty(screen, 'width', {
                get: () => selectedScreen.width,
            });

            Object.defineProperty(screen, 'height', {
                get: () => selectedScreen.height,
            });

            Object.defineProperty(screen, 'availWidth', {
                get: () => selectedScreen.availWidth,
            });

            Object.defineProperty(screen, 'availHeight', {
                get: () => selectedScreen.availHeight,
            });

            Object.defineProperty(screen, 'colorDepth', {
                get: () => selectedScreen.colorDepth,
            });

            Object.defineProperty(screen, 'pixelDepth', {
                get: () => selectedScreen.pixelDepth,
            });

            // Hardware concurrency spoofing
            const coreOptions = [2, 4, 6, 8, 12, 16];
            const selectedCores = coreOptions[Math.floor(Math.random() * coreOptions.length)];

            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => selectedCores,
            });

            // Advanced timezone spoofing with French locale
            const frenchTimezones = [
                { offset: -60, name: 'Europe/Paris' },
                { offset: -60, name: 'Europe/Brussels' },
                { offset: -60, name: 'Europe/Luxembourg' },
                { offset: -60, name: 'Europe/Monaco' }
            ];

            const selectedTimezone = frenchTimezones[Math.floor(Math.random() * frenchTimezones.length)];

            // Override timezone offset
            Date.prototype.getTimezoneOffset = function() {
                return selectedTimezone.offset;
            };

            // Override toLocaleString to use French formatting
            const originalToLocaleString = Date.prototype.toLocaleString;
            Date.prototype.toLocaleString = function(locales, options) {
                return originalToLocaleString.call(this, 'fr-FR', options);
            };

            // Override Intl.DateTimeFormat to default to French
            if (window.Intl && window.Intl.DateTimeFormat) {
                const OriginalDateTimeFormat = window.Intl.DateTimeFormat;
                window.Intl.DateTimeFormat = function(locales, options) {
                    if (!locales) locales = 'fr-FR';
                    return new OriginalDateTimeFormat(locales, options);
                };
                Object.setPrototypeOf(window.Intl.DateTimeFormat, OriginalDateTimeFormat);
            }
            """

            driver.execute_script(stealth_js)

            # Additional advanced anti-detection techniques
            try:
                driver.execute_script("""
                    // Advanced headless detection bypass
                    try {
                        Object.defineProperty(navigator, 'webdriver', {
                            get: () => undefined,
                            configurable: true
                        });

                        // Mock media devices for headless detection
                        if (navigator.mediaDevices) {
                            const originalEnumerateDevices = navigator.mediaDevices.enumerateDevices;
                            navigator.mediaDevices.enumerateDevices = function() {
                                return Promise.resolve([
                                    {
                                        deviceId: 'default',
                                        kind: 'audioinput',
                                        label: 'Default - Microphone (Realtek High Definition Audio)',
                                        groupId: 'group1'
                                    },
                                    {
                                        deviceId: 'communications',
                                        kind: 'audioinput',
                                        label: 'Communications - Microphone (Realtek High Definition Audio)',
                                        groupId: 'group1'
                                    },
                                    {
                                        deviceId: 'default',
                                        kind: 'audiooutput',
                                        label: 'Default - Speakers (Realtek High Definition Audio)',
                                        groupId: 'group2'
                                    }
                                ]);
                            };
                        }

                        // Mock battery API
                        if (navigator.getBattery) {
                            navigator.getBattery = function() {
                                return Promise.resolve({
                                    charging: true,
                                    chargingTime: 0,
                                    dischargingTime: Infinity,
                                    level: 1
                                });
                            };
                        }

                        // Mock connection API
                        if (navigator.connection) {
                            Object.defineProperty(navigator, 'connection', {
                                get: () => ({
                                    effectiveType: '4g',
                                    rtt: 50,
                                    downlink: 10,
                                    saveData: false
                                }),
                                configurable: true
                            });
                        }

                        // Override outerHeight/outerWidth for headless detection
                        Object.defineProperty(window, 'outerHeight', {
                            get: () => window.innerHeight,
                            configurable: true
                        });

                        Object.defineProperty(window, 'outerWidth', {
                            get: () => window.innerWidth,
                            configurable: true
                        });

                    } catch (e) {
                        navigator.webdriver = undefined;
                    }
                """)
            except Exception as e:
                self.logger.warning(f"Advanced anti-detection failed: {str(e)}")

            self.logger.info("Advanced stealth techniques applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying stealth techniques: {str(e)}")

    def _apply_fingerprint_randomization(self, driver):
        """Apply comprehensive fingerprint randomization techniques with profile-specific seeds"""
        try:
            # Get profile-specific fingerprint seeds
            fingerprint = {}
            canvas_seed = fingerprint.get('canvas_seed', random.randint(1, 1000000))
            webgl_seed = fingerprint.get('webgl_seed', random.randint(1, 1000000))
            audio_seed = fingerprint.get('audio_seed', random.randint(1, 1000000))
            font_seed = fingerprint.get('font_seed', random.randint(1, 1000000))
            screen_seed = fingerprint.get('screen_seed', random.randint(1, 1000000))

            # Apply all fingerprinting protections
            self._apply_canvas_fingerprinting(driver, canvas_seed)
            self._apply_webgl_fingerprinting(driver, webgl_seed)
            self._apply_audio_fingerprinting(driver, audio_seed)
            self._apply_font_fingerprinting(driver, font_seed)
            self._apply_screen_fingerprinting(driver, screen_seed)

            # Phase 3.2: Apply network-level stealth protections
            self._apply_network_stealth_protection(driver)

        except Exception as e:
            self.logger.error(f"Error applying fingerprint randomization: {str(e)}")

    def _apply_canvas_fingerprinting(self, driver, canvas_seed):
        """Apply enhanced canvas fingerprinting protection"""
        try:
            canvas_js = f"""
            // Profile-specific seeded random function
            function seededRandom(seed) {{
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }}

            // Use profile-specific canvas seed for consistent fingerprinting
            const profileCanvasSeed = {canvas_seed};

            // Override canvas toDataURL for consistent fingerprinting
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function() {{
                const result = originalToDataURL.apply(this, arguments);
                // Add profile-specific noise to canvas data
                const noise = seededRandom(profileCanvasSeed + this.width + this.height);
                return result + String.fromCharCode(Math.floor(noise * 26) + 97);
            }};

            // Override getImageData for additional protection
            const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
            CanvasRenderingContext2D.prototype.getImageData = function() {{
                const imageData = originalGetImageData.apply(this, arguments);
                const noise = seededRandom(profileCanvasSeed + imageData.width + imageData.height);

                // Add subtle noise to image data
                for (let i = 0; i < imageData.data.length; i += 4) {{
                    const pixelNoise = seededRandom(profileCanvasSeed + i);
                    if (pixelNoise > 0.99) {{ // Only modify 1% of pixels
                        imageData.data[i] = Math.min(255, imageData.data[i] + Math.floor(pixelNoise * 3) - 1);
                        imageData.data[i + 1] = Math.min(255, imageData.data[i + 1] + Math.floor(pixelNoise * 3) - 1);
                        imageData.data[i + 2] = Math.min(255, imageData.data[i + 2] + Math.floor(pixelNoise * 3) - 1);
                    }}
                }}

                return imageData;
            }};

            // Override fillText and strokeText for text rendering protection
            const originalFillText = CanvasRenderingContext2D.prototype.fillText;
            CanvasRenderingContext2D.prototype.fillText = function(text, x, y, maxWidth) {{
                const noise = seededRandom(profileCanvasSeed + text.length + x + y);
                const offsetX = (noise - 0.5) * 0.1;
                const offsetY = (noise - 0.5) * 0.1;
                return originalFillText.call(this, text, x + offsetX, y + offsetY, maxWidth);
            }};

            const originalStrokeText = CanvasRenderingContext2D.prototype.strokeText;
            CanvasRenderingContext2D.prototype.strokeText = function(text, x, y, maxWidth) {{
                const noise = seededRandom(profileCanvasSeed + text.length + x + y);
                const offsetX = (noise - 0.5) * 0.1;
                const offsetY = (noise - 0.5) * 0.1;
                return originalStrokeText.call(this, text, x + offsetX, y + offsetY, maxWidth);
            }};
            """

            driver.execute_script(canvas_js)
            self.logger.info("Enhanced canvas fingerprint randomization applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying canvas fingerprinting: {str(e)}")

    def _apply_webgl_fingerprinting(self, driver, webgl_seed):
        """Apply enhanced WebGL fingerprinting protection"""
        try:
            webgl_js = f"""
            // Profile-specific seeded random function
            function seededRandom(seed) {{
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }}

            // Use profile-specific WebGL seed
            const profileWebGLSeed = {webgl_seed};

            // WebGL context spoofing
            const getContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {{
                if (contextType === 'webgl' || contextType === 'experimental-webgl' || contextType === 'webgl2') {{
                    const context = getContext.call(this, contextType, contextAttributes);
                    if (context) {{
                        // Spoof GPU vendor and renderer
                        const vendors = ['Intel Inc.', 'NVIDIA Corporation', 'AMD', 'Qualcomm'];
                        const renderers = [
                            'Intel(R) HD Graphics 620',
                            'NVIDIA GeForce GTX 1060',
                            'AMD Radeon RX 580',
                            'Intel(R) UHD Graphics 630'
                        ];

                        const vendorIndex = Math.floor(seededRandom(profileWebGLSeed) * vendors.length);
                        const rendererIndex = Math.floor(seededRandom(profileWebGLSeed + 100) * renderers.length);

                        const originalGetParameter = context.getParameter;
                        context.getParameter = function(parameter) {{
                            if (parameter === context.VENDOR) {{
                                return vendors[vendorIndex];
                            }}
                            if (parameter === context.RENDERER) {{
                                return renderers[rendererIndex];
                            }}
                            if (parameter === context.VERSION) {{
                                return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
                            }}
                            if (parameter === context.SHADING_LANGUAGE_VERSION) {{
                                return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
                            }}
                            return originalGetParameter.call(this, parameter);
                        }};

                        // Spoof supported extensions
                        const originalGetSupportedExtensions = context.getSupportedExtensions;
                        context.getSupportedExtensions = function() {{
                            const extensions = originalGetSupportedExtensions.call(this);
                            const noise = seededRandom(profileWebGLSeed + 200);
                            // Randomly remove some extensions based on seed
                            return extensions.filter((ext, index) => seededRandom(profileWebGLSeed + index) > 0.3);
                        }};
                    }}
                    return context;
                }}
                return getContext.call(this, contextType, contextAttributes);
            }};
            """

            driver.execute_script(webgl_js)
            self.logger.info("Enhanced WebGL fingerprint spoofing applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying WebGL fingerprinting: {str(e)}")

    def _apply_audio_fingerprinting(self, driver, audio_seed):
        """Apply enhanced audio fingerprinting protection"""
        try:
            audio_js = f"""
            // Profile-specific seeded random function
            function seededRandom(seed) {{
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }}

            // Use profile-specific audio seed
            const profileAudioSeed = {audio_seed};

            // Audio context fingerprinting protection
            if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {{
                const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;

                function PatchedAudioContext() {{
                    const context = new OriginalAudioContext();

                    // Add noise to audio fingerprinting
                    const originalCreateOscillator = context.createOscillator;
                    context.createOscillator = function() {{
                        const oscillator = originalCreateOscillator.call(this);
                        const originalFrequency = oscillator.frequency.value;
                        oscillator.frequency.value = originalFrequency + (seededRandom(profileAudioSeed) * 0.1 - 0.05);
                        return oscillator;
                    }};

                    // Patch createAnalyser for additional protection
                    const originalCreateAnalyser = context.createAnalyser;
                    context.createAnalyser = function() {{
                        const analyser = originalCreateAnalyser.call(this);
                        const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                        analyser.getFloatFrequencyData = function(array) {{
                            originalGetFloatFrequencyData.call(this, array);
                            // Add subtle noise to frequency data
                            for (let i = 0; i < array.length; i++) {{
                                const noise = seededRandom(profileAudioSeed + i);
                                array[i] += (noise - 0.5) * 0.01;
                            }}
                        }};
                        return analyser;
                    }};

                    // Patch createDynamicsCompressor
                    const originalCreateDynamicsCompressor = context.createDynamicsCompressor;
                    context.createDynamicsCompressor = function() {{
                        const compressor = originalCreateDynamicsCompressor.call(this);
                        const noise = seededRandom(profileAudioSeed + 1000);
                        compressor.threshold.value = -24 + (noise - 0.5) * 2;
                        compressor.knee.value = 30 + (noise - 0.5) * 10;
                        compressor.ratio.value = 12 + (noise - 0.5) * 4;
                        compressor.attack.value = 0.003 + (noise - 0.5) * 0.002;
                        compressor.release.value = 0.25 + (noise - 0.5) * 0.1;
                        return compressor;
                    }};

                    return context;
                }}

                window.AudioContext = PatchedAudioContext;
                if (window.webkitAudioContext) {{
                    window.webkitAudioContext = PatchedAudioContext;
                }}
            }}
            """

            driver.execute_script(audio_js)
            self.logger.info("Enhanced audio fingerprint protection applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying audio fingerprinting: {str(e)}")

    def _apply_font_fingerprinting(self, driver, font_seed):
        """Apply font fingerprinting protection"""
        try:
            # Get profile-specific font list
            fingerprint = {}
            font_list = fingerprint.get('font_list', [])

            font_js = f"""
            // Profile-specific seeded random function
            function seededRandom(seed) {{
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }}

            // Use profile-specific font seed
            const profileFontSeed = {font_seed};
            const profileFontList = {font_list};

            // Override font detection methods
            if (typeof document !== 'undefined') {{
                // Override document.fonts if available
                if (document.fonts && document.fonts.check) {{
                    const originalCheck = document.fonts.check;
                    document.fonts.check = function(font, text) {{
                        const fontFamily = font.split(' ').pop().replace(/['"]/g, '');
                        // Return true only for fonts in our profile list
                        if (profileFontList.includes(fontFamily)) {{
                            return originalCheck.call(this, font, text);
                        }}
                        return false;
                    }};
                }}

                // Override font measurement techniques
                const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
                const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');

                if (originalOffsetWidth && originalOffsetHeight) {{
                    Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {{
                        get: function() {{
                            const width = originalOffsetWidth.get.call(this);
                            if (this.style && this.style.fontFamily) {{
                                const noise = seededRandom(profileFontSeed + width);
                                return Math.round(width + (noise - 0.5) * 0.5);
                            }}
                            return width;
                        }}
                    }});

                    Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {{
                        get: function() {{
                            const height = originalOffsetHeight.get.call(this);
                            if (this.style && this.style.fontFamily) {{
                                const noise = seededRandom(profileFontSeed + height);
                                return Math.round(height + (noise - 0.5) * 0.5);
                            }}
                            return height;
                        }}
                    }});
                }}

                // Override canvas text measurement
                if (CanvasRenderingContext2D.prototype.measureText) {{
                    const originalMeasureText = CanvasRenderingContext2D.prototype.measureText;
                    CanvasRenderingContext2D.prototype.measureText = function(text) {{
                        const metrics = originalMeasureText.call(this, text);
                        const noise = seededRandom(profileFontSeed + text.length);

                        // Add slight variations to text metrics
                        if (metrics.width) {{
                            metrics.width += (noise - 0.5) * 0.1;
                        }}

                        return metrics;
                    }};
                }}
            }}
            """

            driver.execute_script(font_js)
            self.logger.info("Font fingerprint protection applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying font fingerprinting: {str(e)}")

    def _apply_screen_fingerprinting(self, driver, screen_seed):
        """Apply enhanced screen fingerprinting protection"""
        try:
            # Get profile-specific screen properties
            fingerprint = {}
            screen_resolution = fingerprint.get('screen_resolution', {'width': 1920, 'height': 1080})
            screen_properties = fingerprint.get('screen_properties', {'colorDepth': 24, 'pixelDepth': 24, 'orientation': 'landscape-primary'})

            screen_js = f"""
            // Profile-specific seeded random function
            function seededRandom(seed) {{
                const x = Math.sin(seed) * 10000;
                return x - Math.floor(x);
            }}

            // Use profile-specific screen seed and properties
            const profileScreenSeed = {screen_seed};
            const profileWidth = {screen_resolution['width']};
            const profileHeight = {screen_resolution['height']};
            const profileColorDepth = {screen_properties['colorDepth']};
            const profilePixelDepth = {screen_properties['pixelDepth']};
            const profileOrientation = '{screen_properties['orientation']}';

            // Override screen properties safely
            if (typeof screen !== 'undefined') {{
                try {{
                    // Check if properties can be redefined before attempting
                    const screenProps = ['width', 'height', 'availWidth', 'availHeight', 'colorDepth', 'pixelDepth'];
                    const propsToDefine = {{}};

                    screenProps.forEach(prop => {{
                        try {{
                            const descriptor = Object.getOwnPropertyDescriptor(screen, prop);
                            if (!descriptor || descriptor.configurable !== false) {{
                                propsToDefine[prop] = {{
                                    get: function() {{
                                        switch(prop) {{
                                            case 'width': return profileWidth;
                                            case 'height': return profileHeight;
                                            case 'availWidth': return profileWidth;
                                            case 'availHeight': return profileHeight - 40;
                                            case 'colorDepth': return profileColorDepth;
                                            case 'pixelDepth': return profilePixelDepth;
                                            default: return this['_original_' + prop] || 0;
                                        }}
                                    }},
                                    configurable: true,
                                    enumerable: true
                                }};
                            }}
                        }} catch (e) {{
                            // Property check failed, skip this property
                        }}
                    }});

                    if (Object.keys(propsToDefine).length > 0) {{
                        Object.defineProperties(screen, propsToDefine);
                    }}

                    // Override screen orientation if available
                    if (screen.orientation) {{
                        try {{
                            const orientationDescriptor = Object.getOwnPropertyDescriptor(screen.orientation, 'type');
                            if (!orientationDescriptor || orientationDescriptor.configurable !== false) {{
                                Object.defineProperty(screen.orientation, 'type', {{
                                    get: function() {{ return profileOrientation; }},
                                    configurable: true,
                                    enumerable: true
                                }});
                            }}
                        }} catch (e) {{
                            // Orientation override failed, continue
                        }}
                    }}
                }} catch (e) {{
                    // Screen property override failed, continue with other fingerprinting
                }}
            }}

            // Override window dimensions to match screen safely
            try {{
                const windowProps = ['innerWidth', 'innerHeight', 'outerWidth', 'outerHeight'];
                const windowPropsToDefine = {{}};

                windowProps.forEach(prop => {{
                    try {{
                        const descriptor = Object.getOwnPropertyDescriptor(window, prop);
                        if (!descriptor || descriptor.configurable !== false) {{
                            windowPropsToDefine[prop] = {{
                                get: function() {{
                                    switch(prop) {{
                                        case 'innerWidth': return profileWidth;
                                        case 'innerHeight': return profileHeight - 100;
                                        case 'outerWidth': return profileWidth;
                                        case 'outerHeight': return profileHeight;
                                        default: return this['_original_' + prop] || 0;
                                    }}
                                }},
                                configurable: true,
                                enumerable: true
                            }};
                        }}
                    }} catch (e) {{
                        // Property check failed, skip this property
                    }}
                }});

                if (Object.keys(windowPropsToDefine).length > 0) {{
                    Object.defineProperties(window, windowPropsToDefine);
                }}
            }} catch (e) {{
                // Window property override failed, continue
            }}

            // Override devicePixelRatio safely
            try {{
                const pixelRatioDescriptor = Object.getOwnPropertyDescriptor(window, 'devicePixelRatio');
                if (!pixelRatioDescriptor || pixelRatioDescriptor.configurable !== false) {{
                    const noise = seededRandom(profileScreenSeed);
                    const pixelRatios = [1, 1.25, 1.5, 2];
                    const selectedRatio = pixelRatios[Math.floor(noise * pixelRatios.length)];

                    Object.defineProperty(window, 'devicePixelRatio', {{
                        get: function() {{ return selectedRatio; }},
                        configurable: true,
                        enumerable: true
                    }});
                }}
            }} catch (e) {{
                // DevicePixelRatio override failed, continue
            }}
            """

            driver.execute_script(screen_js)
            self.logger.info("Enhanced screen fingerprint protection applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying screen fingerprinting: {str(e)}")

    def _apply_network_stealth_protection(self, driver):
        """Apply Phase 3.2 network-level stealth protections via JavaScript"""
        try:
            network_stealth_js = """
            // Phase 3.2: Network-Level Stealth Protection

            // Enhanced WebRTC IP leak protection
            (function() {
                try {
                    // Override RTCPeerConnection to block IP leaks
                    const OriginalRTCPeerConnection = window.RTCPeerConnection ||
                                                    window.webkitRTCPeerConnection ||
                                                    window.mozRTCPeerConnection;

                    if (OriginalRTCPeerConnection) {
                        function BlockedRTCPeerConnection() {
                            throw new Error('WebRTC is disabled for privacy protection');
                        }

                        // Block all WebRTC constructors
                        window.RTCPeerConnection = BlockedRTCPeerConnection;
                        window.webkitRTCPeerConnection = BlockedRTCPeerConnection;
                        window.mozRTCPeerConnection = BlockedRTCPeerConnection;

                        // Block related WebRTC APIs
                        if (navigator.mediaDevices) {
                            navigator.mediaDevices.getUserMedia = function() {
                                return Promise.reject(new Error('Media access blocked for privacy'));
                            };
                            navigator.mediaDevices.getDisplayMedia = function() {
                                return Promise.reject(new Error('Screen sharing blocked for privacy'));
                            };
                        }

                        // Block legacy getUserMedia
                        if (navigator.getUserMedia) {
                            navigator.getUserMedia = function() {
                                throw new Error('Media access blocked for privacy');
                            };
                        }

                        console.log('WebRTC IP leak protection applied');
                    }
                } catch (e) {
                    console.warn('WebRTC protection error:', e);
                }
            })();

            // DNS leak protection via fetch/XMLHttpRequest monitoring
            (function() {
                try {
                    // Monitor and potentially block direct IP requests
                    const originalFetch = window.fetch;
                    window.fetch = function(url, options) {
                        // Check if URL contains direct IP address
                        const ipRegex = /^https?:\/\/(?:\d{1,3}\.){3}\d{1,3}/;
                        if (typeof url === 'string' && ipRegex.test(url)) {
                            console.warn('Blocked direct IP request for privacy:', url);
                            return Promise.reject(new Error('Direct IP requests blocked'));
                        }
                        return originalFetch.apply(this, arguments);
                    };

                    // Monitor XMLHttpRequest for IP leaks
                    const originalXHROpen = XMLHttpRequest.prototype.open;
                    XMLHttpRequest.prototype.open = function(method, url) {
                        const ipRegex = /^https?:\/\/(?:\d{1,3}\.){3}\d{1,3}/;
                        if (typeof url === 'string' && ipRegex.test(url)) {
                            console.warn('Blocked direct IP XHR request for privacy:', url);
                            throw new Error('Direct IP requests blocked');
                        }
                        return originalXHROpen.apply(this, arguments);
                    };

                    console.log('DNS leak protection applied');
                } catch (e) {
                    console.warn('DNS protection error:', e);
                }
            })();

            // TLS/SSL fingerprinting protection
            (function() {
                try {
                    // Override crypto.subtle to add noise to cryptographic operations
                    if (window.crypto && window.crypto.subtle) {
                        const originalGenerateKey = window.crypto.subtle.generateKey;
                        window.crypto.subtle.generateKey = function() {
                            // Add slight delay to randomize timing
                            return new Promise((resolve, reject) => {
                                setTimeout(() => {
                                    originalGenerateKey.apply(window.crypto.subtle, arguments)
                                        .then(resolve)
                                        .catch(reject);
                                }, Math.random() * 10);
                            });
                        };
                    }

                    console.log('TLS fingerprinting protection applied');
                } catch (e) {
                    console.warn('TLS protection error:', e);
                }
            })();

            // HTTP/2 fingerprinting protection via header randomization
            (function() {
                try {
                    // Override Request constructor to randomize headers
                    const OriginalRequest = window.Request;
                    window.Request = function(input, init) {
                        if (init && init.headers) {
                            // Add random headers to mask HTTP/2 fingerprinting
                            const randomHeaders = {
                                'Accept-Encoding': Math.random() > 0.5 ? 'gzip, deflate, br' : 'gzip, deflate',
                                'Cache-Control': Math.random() > 0.5 ? 'no-cache' : 'max-age=0',
                                'Pragma': Math.random() > 0.3 ? 'no-cache' : undefined
                            };

                            Object.keys(randomHeaders).forEach(key => {
                                if (randomHeaders[key] !== undefined) {
                                    init.headers[key] = randomHeaders[key];
                                }
                            });
                        }
                        return new OriginalRequest(input, init);
                    };

                    console.log('HTTP/2 fingerprinting protection applied');
                } catch (e) {
                    console.warn('HTTP/2 protection error:', e);
                }
            })();

            // Network timing attack protection
            (function() {
                try {
                    // Add noise to performance.now() to prevent timing attacks
                    const originalNow = performance.now;
                    let timeOffset = Math.random() * 10;

                    performance.now = function() {
                        return originalNow.call(this) + timeOffset + (Math.random() - 0.5) * 2;
                    };

                    console.log('Network timing protection applied');
                } catch (e) {
                    console.warn('Timing protection error:', e);
                }
            })();
            """

            driver.execute_script(network_stealth_js)
            self.logger.info("Phase 3.2 network-level stealth protections applied successfully")

        except Exception as e:
            self.logger.error(f"Error applying network stealth protection: {str(e)}")

    def _apply_bot_detection_bypass(self, driver):
        """
        Phase 4.3: Advanced Bot Detection Bypass for Cloudflare, Distil, and other systems
        """
        try:
            self.logger.info("Applying Phase 4.3: Bot Detection Bypass techniques...")

            # Apply comprehensive bot detection bypass
            bot_bypass_js = """
            // Phase 4.3: Advanced Bot Detection Bypass

            // 1. Cloudflare Challenge Bypass
            (function() {
                // Override Cloudflare's challenge detection methods
                const cfChallengeBypass = {
                    // Spoof browser environment for CF challenges
                    spoofEnvironment: function() {
                        // Hide automation indicators that CF specifically looks for
                        Object.defineProperty(navigator, 'webdriver', {
                            get: () => undefined,
                            configurable: true
                        });

                        // Remove Chrome automation flags
                        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
                        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;

                        // Spoof Chrome runtime
                        if (!window.chrome) {
                            window.chrome = {
                                runtime: {
                                    onConnect: undefined,
                                    onMessage: undefined
                                },
                                app: {
                                    isInstalled: false
                                }
                            };
                        }

                        // Add realistic plugin array
                        Object.defineProperty(navigator, 'plugins', {
                            get: () => {
                                const plugins = [
                                    {
                                        name: 'Chrome PDF Plugin',
                                        filename: 'internal-pdf-viewer',
                                        description: 'Portable Document Format'
                                    },
                                    {
                                        name: 'Chrome PDF Viewer',
                                        filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai',
                                        description: ''
                                    },
                                    {
                                        name: 'Native Client',
                                        filename: 'internal-nacl-plugin',
                                        description: ''
                                    }
                                ];
                                plugins.length = 3;
                                return plugins;
                            },
                            configurable: true
                        });
                    },

                    // Bypass CF's mouse movement detection
                    simulateHumanBehavior: function() {
                        // Override mouse event properties
                        const originalAddEventListener = EventTarget.prototype.addEventListener;
                        EventTarget.prototype.addEventListener = function(type, listener, options) {
                            if (type === 'mousemove' || type === 'click' || type === 'mousedown') {
                                const wrappedListener = function(event) {
                                    // Add human-like timing variations
                                    const humanDelay = Math.random() * 50 + 10;
                                    setTimeout(() => {
                                        if (typeof listener === 'function') {
                                            listener.call(this, event);
                                        }
                                    }, humanDelay);
                                };
                                return originalAddEventListener.call(this, type, wrappedListener, options);
                            }
                            return originalAddEventListener.call(this, type, listener, options);
                        };
                    },

                    // Bypass CF's timing analysis
                    spoofTiming: function() {
                        // Override performance.now() to add realistic variations
                        const originalNow = performance.now;
                        let timeOffset = Math.random() * 1000;

                        performance.now = function() {
                            timeOffset += Math.random() * 2 - 1; // Add small random drift
                            return originalNow.call(this) + timeOffset;
                        };

                        // Override Date.now() similarly
                        const originalDateNow = Date.now;
                        Date.now = function() {
                            return originalDateNow() + Math.floor(timeOffset);
                        };
                    }
                };

                // Apply Cloudflare bypass techniques
                cfChallengeBypass.spoofEnvironment();
                cfChallengeBypass.simulateHumanBehavior();
                cfChallengeBypass.spoofTiming();

                console.log('Cloudflare bypass techniques applied');
            })();
            """

            try:
                driver.execute_script(bot_bypass_js)
                self.logger.info("Phase 4.3: Cloudflare Bot Detection Bypass applied successfully")
            except Exception as e:
                self.logger.warning(f"Cloudflare bypass script failed (non-critical): {str(e)}")

            # Apply Distil Networks and other bot detection bypasses
            self._apply_distil_bypass(driver)
            self._apply_general_bot_bypass(driver)

        except Exception as e:
            self.logger.error(f"Error applying bot detection bypass: {str(e)}")

    def _apply_distil_bypass(self, driver):
        """Apply Distil Networks specific bot detection bypass"""
        try:
            distil_bypass_js = """
            // 2. Distil Networks Bypass
            (function() {
                // Safe property definition helper
                function safeDefineProperty(obj, prop, descriptor) {
                    try {
                        const existingDescriptor = Object.getOwnPropertyDescriptor(obj, prop);
                        if (!existingDescriptor || existingDescriptor.configurable !== false) {
                            Object.defineProperty(obj, prop, descriptor);
                            return true;
                        }
                    } catch (e) {
                        // Property cannot be redefined, skip silently
                    }
                    return false;
                }

                const distilBypass = {
                    // Hide from Distil's bot detection
                    hideAutomation: function() {
                        // Distil looks for specific automation signatures
                        const automationProps = [
                            'webdriver',
                            '__webdriver_script_fn',
                            '__selenium_unwrapped',
                            '__webdriver_unwrapped',
                            '__driver_evaluate',
                            '__webdriver_evaluate',
                            '__selenium_evaluate',
                            '__fxdriver_evaluate',
                            '__driver_unwrapped',
                            '__fxdriver_unwrapped',
                            '__webdriver_script_func'
                        ];

                        automationProps.forEach(prop => {
                            try {
                                delete window[prop];
                                delete document[prop];
                                if (navigator[prop]) {
                                    Object.defineProperty(navigator, prop, {
                                        get: () => undefined,
                                        configurable: true
                                    });
                                }
                            } catch (e) {
                                // Property deletion failed, continue
                            }
                        });
                    },

                    // Spoof browser capabilities that Distil checks
                    spoofCapabilities: function() {
                        // Override toString methods that reveal automation
                        const originalToString = Function.prototype.toString;
                        Function.prototype.toString = function() {
                            if (this === navigator.webdriver) {
                                return 'function webdriver() { [native code] }';
                            }
                            return originalToString.call(this);
                        };

                        // Spoof iframe detection using safe property definition
                        safeDefineProperty(window, 'top', {
                            get: () => window,
                            configurable: true
                        });

                        safeDefineProperty(window, 'parent', {
                            get: () => window,
                            configurable: true
                        });
                    },

                    // Bypass Distil's behavioral analysis
                    simulateRealUser: function() {
                        // Add realistic entropy to mouse movements
                        let mouseX = 0, mouseY = 0;
                        const mouseMoveHandler = function(e) {
                            mouseX = e.clientX + (Math.random() - 0.5) * 2;
                            mouseY = e.clientY + (Math.random() - 0.5) * 2;
                        };

                        document.addEventListener('mousemove', mouseMoveHandler, true);

                        // Simulate periodic mouse movements
                        setInterval(() => {
                            const event = new MouseEvent('mousemove', {
                                clientX: mouseX + (Math.random() - 0.5) * 10,
                                clientY: mouseY + (Math.random() - 0.5) * 10,
                                bubbles: true
                            });
                            document.dispatchEvent(event);
                        }, 1000 + Math.random() * 2000);
                    }
                };

                // Apply Distil bypass techniques
                distilBypass.hideAutomation();
                distilBypass.spoofCapabilities();
                distilBypass.simulateRealUser();

                console.log('Distil Networks bypass techniques applied');
            })();
            """

            try:
                driver.execute_script(distil_bypass_js)
                self.logger.info("Distil Networks bypass applied successfully")
            except Exception as js_error:
                self.logger.warning(f"Distil bypass script failed (non-critical): {str(js_error)}")

        except Exception as e:
            self.logger.warning(f"Error applying Distil bypass (non-critical): {str(e)}")

    def _apply_general_bot_bypass(self, driver):
        """Apply general bot detection bypass techniques for various systems"""
        try:
            general_bypass_js = """
            // 3. General Bot Detection Bypass
            (function() {
                // Safe property definition helper
                function safeDefineProperty(obj, prop, descriptor) {
                    try {
                        const existingDescriptor = Object.getOwnPropertyDescriptor(obj, prop);
                        if (!existingDescriptor || existingDescriptor.configurable !== false) {
                            Object.defineProperty(obj, prop, descriptor);
                            return true;
                        }
                    } catch (e) {
                        // Property cannot be redefined, skip silently
                    }
                    return false;
                }

                const generalBypass = {
                    // Advanced headless detection bypass
                    bypassHeadlessDetection: function() {
                        // Override headless-specific properties safely
                        safeDefineProperty(navigator, 'webdriver', {
                            get: () => undefined,
                            configurable: true
                        });

                        // Spoof missing properties in headless mode
                        if (!window.outerHeight || !window.outerWidth) {
                            safeDefineProperty(window, 'outerHeight', {
                                get: () => screen.height,
                                configurable: true
                            });
                            safeDefineProperty(window, 'outerWidth', {
                                get: () => screen.width,
                                configurable: true
                            });
                        }

                        // Add missing notification permission
                        if (!window.Notification) {
                            window.Notification = {
                                permission: 'default',
                                requestPermission: () => Promise.resolve('default')
                            };
                        }

                        // Spoof battery API
                        if (!navigator.getBattery) {
                            navigator.getBattery = () => Promise.resolve({
                                charging: true,
                                chargingTime: 0,
                                dischargingTime: Infinity,
                                level: 1
                            });
                        }
                    },

                    // Advanced automation detection bypass
                    bypassAutomationDetection: function() {
                        // Remove all known automation indicators
                        const automationIndicators = [
                            'webdriver',
                            '__nightmare',
                            '__phantomas',
                            '__selenium_unwrapped',
                            '__webdriver_unwrapped',
                            '__driver_evaluate',
                            '__webdriver_evaluate',
                            '__selenium_evaluate',
                            '__fxdriver_evaluate',
                            '__driver_unwrapped',
                            '__fxdriver_unwrapped',
                            '__webdriver_script_func',
                            'phantom',
                            'callPhantom',
                            '_phantom',
                            'callSelenium',
                            '_selenium',
                            'domAutomation',
                            'domAutomationController'
                        ];

                        automationIndicators.forEach(indicator => {
                            try {
                                delete window[indicator];
                                delete document[indicator];
                                delete navigator[indicator];
                            } catch (e) {
                                // Deletion failed, try to override
                                try {
                                    window[indicator] = undefined;
                                    document[indicator] = undefined;
                                    if (navigator[indicator]) {
                                        Object.defineProperty(navigator, indicator, {
                                            get: () => undefined,
                                            configurable: true
                                        });
                                    }
                                } catch (e2) {
                                    // Override failed too, continue
                                }
                            }
                        });

                        // Override permission queries that reveal automation
                        if (navigator.permissions && navigator.permissions.query) {
                            const originalQuery = navigator.permissions.query;
                            navigator.permissions.query = function(parameters) {
                                return originalQuery(parameters).then(result => {
                                    if (parameters.name === 'notifications') {
                                        return { state: 'granted', onchange: null };
                                    }
                                    return result;
                                });
                            };
                        }
                    },

                    // Behavioral pattern simulation
                    simulateHumanPatterns: function() {
                        // Simulate human-like focus/blur patterns
                        let focusCount = 0;
                        const originalFocus = HTMLElement.prototype.focus;
                        HTMLElement.prototype.focus = function() {
                            focusCount++;
                            // Add slight delay to simulate human reaction time
                            setTimeout(() => {
                                originalFocus.call(this);
                            }, Math.random() * 100 + 50);
                        };

                        // Simulate realistic scroll behavior
                        let lastScrollTime = Date.now();
                        const originalScrollTo = window.scrollTo;
                        window.scrollTo = function(x, y) {
                            const now = Date.now();
                            const timeDiff = now - lastScrollTime;

                            // Prevent too-fast scrolling
                            if (timeDiff < 16) { // ~60fps limit
                                setTimeout(() => {
                                    originalScrollTo.call(this, x, y);
                                }, 16 - timeDiff);
                            } else {
                                originalScrollTo.call(this, x, y);
                            }
                            lastScrollTime = now;
                        };

                        // Add realistic keyboard event timing
                        const originalDispatchEvent = EventTarget.prototype.dispatchEvent;
                        EventTarget.prototype.dispatchEvent = function(event) {
                            if (event.type === 'keydown' || event.type === 'keyup') {
                                // Add human-like typing delays
                                const delay = Math.random() * 50 + 10;
                                setTimeout(() => {
                                    originalDispatchEvent.call(this, event);
                                }, delay);
                                return true;
                            }
                            return originalDispatchEvent.call(this, event);
                        };
                    }
                };

                // Apply all general bypass techniques
                generalBypass.bypassHeadlessDetection();
                generalBypass.bypassAutomationDetection();
                generalBypass.simulateHumanPatterns();

                console.log('General bot detection bypass techniques applied');
            })();
            """

            try:
                driver.execute_script(general_bypass_js)
                self.logger.info("General bot detection bypass applied successfully")
            except Exception as js_error:
                self.logger.warning(f"General bot bypass script failed (non-critical): {str(js_error)}")

        except Exception as e:
            self.logger.warning(f"Error applying general bot bypass (non-critical): {str(e)}")

    def _load_gmx_cookies(self):
        """Load predefined GMX cookies to bypass consent dialogs and improve session persistence.

        Returns:
            bool: True if cookies were loaded successfully, False otherwise
        """
        try:
            self.logger.info("Loading predefined GMX cookies...")

            # Predefined GMX cookies for consent bypass and session persistence
            gmx_cookies = [
                {
                    "name": "consentLevel",
                    "value": "3",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": True,
                    "httpOnly": False
                },
                {
                    "name": "cookieKID",
                    "value": "kid%40autoref%40www.gmx.com",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": False,
                    "httpOnly": False
                },
                {
                    "name": "cookiePartner",
                    "value": "kid%40autoref%40www.gmx.com",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": False,
                    "httpOnly": False
                },
                {
                    "name": "euconsent-bypass",
                    "value": "1",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": False,
                    "httpOnly": False
                },
                {
                    "name": "euconsent-v2",
                    "value": "CQWFlcAQWFlcAAcABBENB3FkAP_gAAAAAAYgJZIR1G7fbXFjeT53YfpkaIwX1dBr6sQhBgTAk2AFzJuQ8JwC12E6MATApqACERIAolRBIQMEHABUAFCAIIAFAADMIESUoAAKICBEABEQAAIQAAgKEgIAEAAIgEBFIhUAmBiA6dLkxciACIAAB0AYgoABCIABAAMBAEAIQBAAAIIAwygAAQBAAIIAAAAAARAIAABBQAAAIAEAAABgSBeAAuACgAKgAcAA8ACCAGQAagA8ACYAFUAN4AfgBCQCGAIgARwAmgBWgDAAGGAMsAc8A7gDvAHtAPsA_QCKAEYAI1ASIBJQC5gGKANoAbgA4gCHYEegSIAnYBQ4CjwFIgLYAXmAw2BkYGSAMzgawBrIDcwHjgSKCADAAHAAkACOAJwAg4BHACaAJWAVCAv8BiwDIR0DcABcAFAAVAA4ACAAF0AMgA1AB4AEwAKsAXABdADeAH6AQwBEACaAE4AKMAVoAwABhgDRAHPAO4A7wB7QD7AP2AigCLAEYgI6AkoBYgC5gF5AMUAbQA3ABxAEOgIvgR6BIgCdgFDgKPAWwAt0BeYDDYGRgZIAyoBlgDMwGsAOLAeOA-sB_YEAQJFDgCwADgALgAkACOAGgARwAywByADogIOAhABHACaAIQASsAqEBagC_wGLAMhAfsBG8CQhCAoAAsACgALgAagBVAC4AG8AYAA54B3AHeARQAlIBcwDFAG0AR6AyMB44D-wJFEABAAaABlgDkAI4AWIB-wEMwI3koDYACwAKAAcAB4AEwAKoAXIBDAEQAI4AUYArQBgADvAH4AXMAxQCHQEXwI9AkQBR4C2AF5wMjAyQBlgDWAIAgSKJACgALgBHAHcAQcAjgCVgF_gMWAfsBDMpApAAXABQAFQAOAAggBkAGgAPAAmABVAD9AIYAiABRgCtAGAANEAc4A74B9gH6ARYAjEBHAElALmAXkAxQBtADcAIdARfAj0CRAE7AKHAWwAvMBhsDIwMkAZZA1gDWQHjgP7AhmBDkCRRQAiABcAEgALgAjgCOAE4AMsAcgA7gCDgFiANeAdsA_4C1AGLAP2LQBgAagDAAHcAocBmYDxywAMAZYBHAEegAAA.f_wAAAAABVAgAAAA",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": True,
                    "httpOnly": False
                },
                {
                    "name": "idcc",
                    "value": "1",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": True,
                    "httpOnly": False
                },
                {
                    "name": "uiconsent",
                    "value": "{%22permissionFeature%22:[%22fullConsent%22]}",
                    "domain": ".gmx.com",
                    "path": "/",
                    "secure": True,
                    "httpOnly": False
                }
            ]

            # Add each cookie to the browser
            cookies_loaded = 0
            for cookie in gmx_cookies:
                try:
                    # Use the appropriate method based on browser type
                    if hasattr(self.browser, 'add_cookie'):
                        self.browser.add_cookie(cookie)
                    elif hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'add_cookie'):
                        self.browser.driver.add_cookie(cookie)
                    else:
                        self.logger.warning("No add_cookie method found on browser")
                        continue

                    cookies_loaded += 1
                    self.logger.debug(f"Loaded cookie: {cookie['name']}")

                except Exception as cookie_error:
                    self.logger.warning(f"Failed to load cookie {cookie['name']}: {str(cookie_error)}")
                    continue

            self.logger.info(f"Successfully loaded {cookies_loaded}/{len(gmx_cookies)} GMX cookies")
            return cookies_loaded > 0

        except Exception as e:
            self.logger.error(f"Error loading GMX cookies: {str(e)}")
            return False






    # ========== COMPATIBILITY METHODS (Original Driver API) ==========

    def go(self, url):
        """Navigate to URL with enhanced error handling and HTTPS proxy fallback"""
        self.url = url
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                self.logger.info(f"Navigating to {url} (attempt {attempt + 1})")

                # Check if browser is still alive before navigation
                """
                try:
                    # Check if we have any windows available using appropriate method for SB context manager or regular WebDriver
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'window_handles'):
                        window_handles = self.browser.driver.window_handles
                        switch_to = self.browser.driver.switch_to
                    elif hasattr(self.browser, 'window_handles'):
                        window_handles = self.browser.window_handles
                        switch_to = self.browser.switch_to
                    else:
                        window_handles = []
                        switch_to = None

                    if len(window_handles) == 0:
                        self.logger.warning("No browser windows available, recreating browser")
                        self._recreate_browser()

                    # Switch to the first available window
                    if switch_to and window_handles:
                        switch_to.window(window_handles[0])
                except Exception as e:
                    self.logger.warning(f"Browser window issue, attempting to recreate: {str(e)}")
                    self._recreate_browser()

                """

                # Set a reasonable page load timeout using appropriate method for SB context manager or regular WebDriver
                try:
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_page_load_timeout'):
                        self.browser.driver.set_page_load_timeout(120)  # 60 seconds timeout
                    elif hasattr(self.browser, 'set_page_load_timeout'):
                        self.browser.set_page_load_timeout(120)  # 60 seconds timeout
                except:
                    pass

                # Navigate to URL using appropriate method for SB context manager or regular WebDriver
                if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'get'):
                    self.browser.driver.get(url)
                elif hasattr(self.browser, 'get'):
                    self.browser.get(url)


                # Check if page loaded successfully
                try:
                    # Get current URL using appropriate method for SB context manager or regular WebDriver
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'current_url'):
                        current_url = self.browser.driver.current_url
                    elif hasattr(self.browser, 'current_url'):
                        current_url = self.browser.current_url
                    else:
                        current_url = None

                    if current_url and current_url != "data:,":
                        self.logger.info(f"Successfully navigated to: {current_url}")

                        # Special verification for Google sign-in pages
                        if "accounts.google.com" in url.lower():
                            if self._verify_google_signin_page():
                                self.logger.info("Google sign-in page verified successfully")
                            else:
                                self.logger.warning("Google sign-in page verification failed, but continuing...")

                        # Load GMX cookies for GMX domains
                        if "gmx.com" in url.lower():
                            self._load_gmx_cookies()

                        # Add random delay to simulate human behavior
                        delay = random.uniform(0.5, 0.9)
                        sleep(delay)

                        # Re-apply stealth techniques after navigation
                        self._reapply_stealth_after_navigation()

                        # Chrome profiles handle session persistence automatically
                        self.logger.debug("Session persistence handled by Chrome profile")

                        return True
                    else:
                        raise Exception("Page did not load properly - blank or data URL")

                except Exception as url_check_error:
                    self.logger.warning(f"Could not verify page load: {str(url_check_error)}")
                    # Continue with the assumption that navigation worked
                    delay = random.uniform(1.0, 1.5)
                    sleep(delay)
                    self._reapply_stealth_after_navigation()
                    return True

            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"Navigation attempt {attempt + 1} failed: {error_msg}")

                # Handle specific navigation errors
                if "ERR_NO_SUPPORTED_PROXIES" in error_msg:
                    self.logger.error("Proxy configuration error - check proxy settings")
                    raise e  # Don't retry for proxy config errors
                elif "ERR_PROXY_CONNECTION_FAILED" in error_msg:
                    self.logger.error("Proxy connection failed - check proxy server")
                    if attempt == max_retries - 1:
                        raise e
                elif "TimeoutException" in error_msg or "timeout" in error_msg.lower():
                    self.logger.warning(f"Navigation timeout on attempt {attempt + 1}")
                    # Continue with retry logic for timeouts
                elif "ERR_INTERNET_DISCONNECTED" in error_msg:
                    self.logger.error("Internet connection lost")
                    raise e  # Don't retry for network disconnection



                if attempt < max_retries - 1:
                    self.logger.info(f"Waiting {retry_delay} seconds before retry...")
                    sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                else:
                    self.logger.error(f"Failed to navigate to {url} after {max_retries} attempts")
                    self.logger.error(f"Final error: {error_msg}")
                    raise e

        # If we get here, all attempts failed
        self.logger.error(f"Navigation to {url} failed after all retry attempts")
        return False

    def get_callback(self):
        print("Using Callback!!")
        try:
            callback = self.execute_js("""
                function findRecaptchaClients() {
                            if (typeof (___grecaptcha_cfg) !== 'undefined') {
                                return Object.entries(___grecaptcha_cfg.clients).map(([cid, client]) => {
                                const data = { id: cid, version: cid >= 10000 ? 'V3' : 'V2' };
                                const objects = Object.entries(client).filter(([_, value]) => value && typeof value === 'object');

                                objects.forEach(([toplevelKey, toplevel]) => {
                                    const found = Object.entries(toplevel).find(([_, value]) => (
                                    value && typeof value === 'object' && 'sitekey' in value && 'size' in value
                                    ));

                                    if (typeof toplevel === 'object' && toplevel instanceof HTMLElement && toplevel['tagName'] === 'DIV'){
                                        data.pageurl = toplevel.baseURI;
                                    }

                                    if (found) {
                                    const [sublevelKey, sublevel] = found;

                                    data.sitekey = sublevel.sitekey;
                                    const callbackKey = data.version === 'V2' ? 'callback' : 'promise-callback';
                                    const callback = sublevel[callbackKey];
                                    if (!callback) {
                                        data.callback = null;
                                        data.function = null;
                                    } else {
                                        data.function = callback;
                                        const keys = [cid, toplevelKey, sublevelKey, callbackKey].map((key) => `['${key}']`).join('');
                                        data.callback = `___grecaptcha_cfg.clients${keys}`;
                                    }
                                    }
                                });
                                return data;
                                });
                            }
                            return [];
                            }
                            const callback = findRecaptchaClients();
                            let callToken = null;
                            if (callback.length > 0) {
                                callToken = callback[0]["callback"];
                            }
                            return callToken;
                            """)
            return callback
        except Exception as e:
            self.logger.error(f"{str(e)}")
            return None

    def _verify_google_signin_page(self):
        """Verify that Google sign-in page loaded correctly"""
        try:
            # Get current URL using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'current_url'):
                current_url = self.browser.driver.current_url
            elif hasattr(self.browser, 'current_url'):
                current_url = self.browser.current_url
            else:
                current_url = None

            # Get page title using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'title'):
                page_title = self.browser.driver.title
            elif hasattr(self.browser, 'title'):
                page_title = self.browser.title
            else:
                page_title = None

            # Check if we're on a Google sign-in related page
            google_signin_indicators = [
                "accounts.google.com",
                "signin",
                "identifier",
                "challenge"
            ]

            url_check = any(indicator in current_url.lower() for indicator in google_signin_indicators)
            title_check = page_title and ("google" in page_title.lower() or "sign" in page_title.lower())

            if url_check or title_check:
                self.logger.info(f"Google sign-in page verified: {current_url}")
                self.logger.info(f"Page title: {page_title}")
                return True
            else:
                self.logger.warning(f"Unexpected page loaded: {current_url}")
                self.logger.warning(f"Page title: {page_title}")
                return False

        except Exception as e:
            self.logger.error(f"Error verifying Google sign-in page: {str(e)}")
            return False

    def _handle_google_signin_issues(self):
        """Handle common Google sign-in page issues"""
        try:
            # Get current URL using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'current_url'):
                current_url = self.browser.driver.current_url
            elif hasattr(self.browser, 'current_url'):
                current_url = self.browser.current_url
            else:
                current_url = None

            # Check for common Google sign-in issues
            if "accounts.google.com/signin/blocked" in current_url:
                self.logger.error("Google account sign-in blocked")
                return False
            elif "accounts.google.com/signin/suspended" in current_url:
                self.logger.error("Google account suspended")
                return False
            elif "accounts.google.com/signin/challenge" in current_url:
                self.logger.warning("Google sign-in challenge detected")
                return True  # Can continue, but needs manual intervention
            elif "myaccount.google.com" in current_url:
                self.logger.info("Already signed in - redirected to My Account")
                return True

            return True

        except Exception as e:
            self.logger.error(f"Error handling Google sign-in issues: {str(e)}")
            return True  # Continue anyway

    def _reapply_stealth_after_navigation(self):
        """Re-apply critical stealth techniques after page navigation"""
        try:
            # Re-apply critical stealth techniques that might be reset after navigation
            post_nav_stealth = """
            // Re-hide webdriver property
            try {
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                    configurable: true
                });
            } catch (e) {
                try {
                    delete navigator.webdriver;
                    navigator.webdriver = undefined;
                } catch (e2) {
                    // Fallback failed
                }
            }

            // Re-apply French language settings
            try {
                Object.defineProperty(navigator, 'language', {
                    get: () => 'fr-FR',
                    configurable: true
                });
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['fr-FR', 'fr', 'en-US', 'en'],
                    configurable: true
                });
            } catch (e) {
                // Language override failed
            }

            // Remove automation flags again
            try {
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
                delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            } catch (e) {
                // Automation flags removal failed
            }
            """

            # Execute post-navigation stealth using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'execute_script'):
                self.browser.driver.execute_script(post_nav_stealth)
            elif hasattr(self.browser, 'execute_script'):
                self.browser.execute_script(post_nav_stealth)
            self.logger.info("Post-navigation stealth techniques applied")

        except Exception as e:
            self.logger.warning(f"Post-navigation stealth application failed: {str(e)}")

    def find_xpath(self, xpath):
        """Find element by XPath with SeleniumBase enhancement"""
        try:
            # Find element using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'find_element'):
                element = self.browser.driver.find_element(By.XPATH, xpath)
            elif hasattr(self.browser, 'find_element'):
                element = self.browser.find_element(By.XPATH, xpath)
            else:
                element = None
            return element
        except Exception as e:
            self.logger.error(f"Error finding element by XPath {xpath}")
            raise e

    def find_xpath_silent(self, xpath):
        """Find element by XPath without logging errors and with zero timeout (for detection purposes)"""
        try:
            # Get appropriate browser and timeouts objects for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver'):
                browser_obj = self.browser.driver
                timeouts_obj = self.browser.driver.timeouts if hasattr(self.browser.driver, 'timeouts') else None
            else:
                browser_obj = self.browser
                timeouts_obj = self.browser.timeouts if hasattr(self.browser, 'timeouts') else None

            # Temporarily set implicit wait to 0 for instant detection
            original_timeout = timeouts_obj.implicit_wait if timeouts_obj else None
            if hasattr(browser_obj, 'implicitly_wait'):
                browser_obj.implicitly_wait(0)

            try:
                if hasattr(browser_obj, 'find_element'):
                    element = browser_obj.find_element(By.XPATH, xpath)
                    return element
                else:
                    return None
            finally:
                # Restore original implicit wait timeout
                if hasattr(browser_obj, 'implicitly_wait') and original_timeout is not None:
                    browser_obj.implicitly_wait(original_timeout)

        except Exception:
            # Ensure timeout is restored even if there's an exception
            try:
                self.browser.implicitly_wait(original_timeout)
            except:
                pass
            return None

    def find_xpath_all(self, xpath):
        """Find all elements by XPath"""
        try:
            # Find elements using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'find_elements'):
                elements = self.browser.driver.find_elements(By.XPATH, xpath)
            elif hasattr(self.browser, 'find_elements'):
                elements = self.browser.find_elements(By.XPATH, xpath)
            else:
                elements = []
            return elements
        except Exception as e:
            self.logger.error(f"Error finding elements by XPath {xpath}: {str(e)}")
            raise e

    def find_css(self, css):
        """Find element by CSS selector"""
        try:
            # Find element using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'find_element'):
                element = self.browser.driver.find_element(By.CSS_SELECTOR, css)
            elif hasattr(self.browser, 'find_element'):
                element = self.browser.find_element(By.CSS_SELECTOR, css)
            else:
                element = None
            return element
        except Exception as e:
            self.logger.error(f"Error finding element by CSS {css}: {str(e)}")
            raise e

    def find_css_silent(self, css):
        """Find element by CSS selector without logging errors and with zero timeout (for detection purposes)"""
        try:
            # Get appropriate browser and timeouts objects for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver'):
                browser_obj = self.browser.driver
                timeouts_obj = self.browser.driver.timeouts if hasattr(self.browser.driver, 'timeouts') else None
            else:
                browser_obj = self.browser
                timeouts_obj = self.browser.timeouts if hasattr(self.browser, 'timeouts') else None

            # Temporarily set implicit wait to 0 for instant detection
            original_timeout = timeouts_obj.implicit_wait if timeouts_obj else None
            if hasattr(browser_obj, 'implicitly_wait'):
                browser_obj.implicitly_wait(0)

            try:
                if hasattr(browser_obj, 'find_element'):
                    element = browser_obj.find_element(By.CSS_SELECTOR, css)
                    return element
                else:
                    return None
            finally:
                # Restore original implicit wait timeout
                if hasattr(browser_obj, 'implicitly_wait') and original_timeout is not None:
                    browser_obj.implicitly_wait(original_timeout)

        except Exception:
            # Ensure timeout is restored even if there's an exception
            try:
                self.browser.implicitly_wait(original_timeout)
            except:
                pass
            return None

    def find_css_all(self, css):
        """Find all elements by CSS selector"""
        try:
            # Find elements using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'find_elements'):
                elements = self.browser.driver.find_elements(By.CSS_SELECTOR, css)
            elif hasattr(self.browser, 'find_elements'):
                elements = self.browser.find_elements(By.CSS_SELECTOR, css)
            else:
                elements = []
            return elements
        except Exception as e:
            self.logger.error(f"Error finding elements by CSS {css}: {str(e)}")
            raise e

    def find_class(self, class_name):
        """Find element by class name"""
        try:
            # Find element using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'find_element'):
                element = self.browser.driver.find_element(By.CLASS_NAME, class_name)
            elif hasattr(self.browser, 'find_element'):
                element = self.browser.find_element(By.CLASS_NAME, class_name)
            else:
                element = None
            return element
        except Exception as e:
            self.logger.error(f"Error finding element by class {class_name}: {str(e)}")
            raise e

    def execute_js(self, js):
        """Execute JavaScript with result return"""
        try:
            # Execute JavaScript using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'execute_script'):
                result = self.browser.driver.execute_script(js)
            elif hasattr(self.browser, 'execute_script'):
                result = self.browser.execute_script(js)
            else:
                result = None
            return result
        except Exception as e:
            self.logger.error(f"Error executing JavaScript: {str(e)}")
            raise e

    def wait_xpath_presence(self, xpath, timeout=120):
        """Wait for element presence by XPath"""
        try:
            return WebDriverWait(self.browser, timeout).until(
                EC.presence_of_element_located((By.XPATH, xpath))
            )
        except TimeoutException:
            self.logger.error(f"Timeout waiting for XPath presence: {xpath}")
            raise

    def wait_css_clickable(self, css, timeout=25):
        """Wait for element to be clickable by CSS"""
        try:
            return WebDriverWait(self.browser, timeout).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, css))
            )
        except TimeoutException:
            self.logger.error(f"Timeout waiting for CSS clickable: {css}")
            raise

    def wait_xpath_frame(self, xpath, timeout=25):
        """Wait for frame and switch to it"""
        try:
            element = WebDriverWait(self.browser, timeout).until(
                EC.frame_to_be_available_and_switch_to_it((By.XPATH, xpath))
            )
            return element
        except TimeoutException:
            self.logger.error(f"Timeout waiting for frame: {xpath}")
            raise


    def running(self):
        """Check if browser is still running"""
        try:
            # Get page title using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'title'):
                title = self.browser.driver.title
            elif hasattr(self.browser, 'title'):
                title = self.browser.title
            else:
                title = None
            if title:
                return True
        except:
            try:
                # Check browser accessibility using appropriate method for SB context manager or regular WebDriver
                if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'current_url'):
                    self.browser.driver.current_url
                elif hasattr(self.browser, 'current_url'):
                    self.browser.current_url
                return True
            except:
                return False

    def this_url(self):
        """Get current URL"""
        try:
            # Get current URL using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'current_url'):
                return self.browser.driver.current_url
            elif hasattr(self.browser, 'current_url'):
                return self.browser.current_url
            else:
                return None
        except Exception as e:
            self.logger.error(f"Error getting current URL: {str(e)}")
            return None

    def title(self):
        """Get page title"""
        try:
            # Get page title using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'title'):
                return self.browser.driver.title
            elif hasattr(self.browser, 'title'):
                return self.browser.title
            else:
                return None
        except Exception as e:
            self.logger.error(f"Error getting page title: {str(e)}")
            return None



    def scrol_down(self, limit=300):
        """Scroll down the page with human-like behavior"""
        try:
            # Use behavioral simulator for human-like scrolling
            if hasattr(self, 'behavioral_simulator'):
                # Convert limit to scroll chunks (each chunk is ~3-5 scroll actions)
                chunk_size = self.behavioral_simulator.rng.randint(3, 7)
                num_chunks = limit // chunk_size

                for i in range(num_chunks):
                    self.behavioral_simulator.human_scroll('down', chunk_size)

                    # Occasional reading pauses
                    if i % 10 == 0 and i > 0:  # Every 10 chunks
                        reading_pause = self.behavioral_simulator.rng.uniform(1.0, 2.1)
                        sleep(reading_pause)

                # Handle remaining scrolls
                remaining = limit % chunk_size
                if remaining > 0:
                    self.behavioral_simulator.human_scroll('down', remaining)
            else:
                # Fallback to original method
                html_tag = self.browser.find_element(By.TAG_NAME, "html")
                for i in range(limit):
                    html_tag.send_keys(Keys.DOWN)
                    if i % 50 == 0:  # Add small delays every 50 scrolls
                        sleep(0.1)
        except Exception as e:
            self.logger.error(f"Error scrolling down: {str(e)}")

    def switch_back(self):
        """Switch back to default content"""
        try:
            # Switch to default content using appropriate method for SB context manager or regular WebDriver
            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'switch_to'):
                self.browser.driver.switch_to.default_content()
            elif hasattr(self.browser, 'switch_to'):
                self.browser.switch_to.default_content()
        except Exception as e:

            self.logger.error(f"Error switching back to default content: {str(e)}")




    # ========== ENHANCED BEHAVIORAL METHODS ==========

    def human_click_element(self, element, double_click=False):
        """Click element with human-like behavior including PyAutoGUI movements"""
        try:
            if hasattr(self, 'behavioral_simulator'):
                # Add natural mouse movement before click
                if hasattr(self.behavioral_simulator, 'pyautogui_available') and self.behavioral_simulator.pyautogui_available:
                    try:
                        location = element.location
                        size = element.size
                        target_x = location['x'] + size['width'] // 2
                        target_y = location['y'] + size['height'] // 2

                        # Natural movement to element
                        self.behavioral_simulator.pyautogui_natural_move(target_x, target_y)

                        # Brief hesitation before click
                        sleep(uniform(0.1, 0.3))
                    except Exception:
                        pass  # Continue with standard method

                self.behavioral_simulator.human_click(element, double_click=double_click)
            else:
                # Fallback to standard click
                if double_click:
                    from selenium.webdriver.common.action_chains import ActionChains
                    ActionChains(self.browser).double_click(element).perform()
                else:
                    element.click()
        except Exception as e:
            self.logger.error(f"Error in human click: {str(e)}")
            # Final fallback
            try:
                element.click()
            except:
                pass

    def human_type_text(self, element, text, clear_first=True):
        """Type text with human-like patterns"""
        try:
            if hasattr(self, 'behavioral_simulator'):
                self.behavioral_simulator.human_type(element, text, clear_first)
            else:
                # Fallback to standard typing
                if clear_first:
                    element.clear()
                element.send_keys(text)
        except Exception as e:
            self.logger.error(f"Error in human typing: {str(e)}")
            # Final fallback
            try:
                if clear_first:
                    element.clear()
                element.send_keys(text)
            except:
                pass

    def human_move_to_element(self, element, offset_x=0, offset_y=0):
        """Move mouse to element with human-like movement"""
        try:
            if hasattr(self, 'behavioral_simulator'):
                self.behavioral_simulator.human_mouse_move(element, offset_x, offset_y)
            else:
                # Fallback to standard move
                from selenium.webdriver.common.action_chains import ActionChains
                ActionChains(self.browser).move_to_element_with_offset(element, offset_x, offset_y).perform()
        except Exception as e:
            self.logger.error(f"Error in human mouse movement: {str(e)}")
            # Final fallback
            try:
                from selenium.webdriver.common.action_chains import ActionChains
                ActionChains(self.browser).move_to_element(element).perform()
            except:
                pass

    def human_scroll_page(self, direction='down', amount=5):
        """Scroll page with human-like behavior"""
        try:
            if hasattr(self, 'behavioral_simulator'):
                self.behavioral_simulator.human_scroll(direction, amount)
            else:
                # Fallback to standard scroll
                from selenium.webdriver.common.keys import Keys
                html_tag = self.browser.find_element(By.TAG_NAME, "html")
                scroll_key = Keys.DOWN if direction.lower() == 'down' else Keys.UP
                for _ in range(amount):
                    html_tag.send_keys(scroll_key)
                    sleep(0.1)
        except Exception as e:
            self.logger.error(f"Error in human scrolling: {str(e)}")

    def simulate_captcha_analysis(self, duration=3.0):
        """Simulate human-like mouse movements while analyzing CAPTCHA"""
        try:
            if hasattr(self, 'behavioral_simulator'):
                self.behavioral_simulator.simulate_reading_movements(duration)
                self.logger.debug("Simulated CAPTCHA analysis movements")
        except Exception as e:
            self.logger.debug(f"CAPTCHA analysis simulation failed: {str(e)}")

    def simulate_page_loading_activity(self):
        """Simulate occasional mouse movements during page loading"""
        try:
            if hasattr(self, 'behavioral_simulator'):
                self.behavioral_simulator.simulate_idle_movements()
        except Exception:
            pass



    def perform_human_actions_after_login(self):
        """
        Perform realistic human actions after successful Gmail login to warm up the account
        and establish natural browsing patterns. This helps avoid detection and makes the
        account appear more legitimate.
        """
        self.logger.info("### Starting Human Actions After Login (Enhanced Driver) ###")

        try:

            # Phase 1: Initial account exploration (2-4 minutes)
            self._explore_google_services_enhanced()

            # Phase 2: Gmail interaction (3-5 minutes)
            self._interact_with_gmail_enhanced()

            # Phase 3: Google Search activity (2-3 minutes)
            self._perform_search_activities_enhanced()

            # Phase 4: Account settings check (1-2 minutes)
            self._check_account_settings_enhanced()

            # Phase 5: Return to main Google page
            self._return_to_google_home_enhanced()

            self.logger.info("### Human Actions After Login Completed Successfully (Enhanced) ###")

        except Exception as e:
            self.logger.error(f"Error during enhanced human actions: {str(e)}")
            # Continue with the main flow even if human actions fail
            pass


    def _explore_google_services_enhanced(self):
        """Phase 1: Explore Google services with enhanced behavioral simulation"""
        self.logger.info("Phase 1: Exploring Google services (Enhanced)...")

        try:
            # Start from Google homepage
            self.go("https://www.google.com")
            sleep(uniform(2.0, 4.0))

            # Simulate reading the page with human-like scrolling
            self.human_scroll_page('down', randint(3, 6))
            sleep(uniform(1.5, 3.0))

            # Visit Google Images briefly with enhanced navigation
            try:
                self.go("https://images.google.com")
                sleep(uniform(2.0, 3.0))

                # Simulate looking at trending images with human-like scrolling
                self.human_scroll_page('down', randint(4, 8))
                sleep(uniform(1.5, 2.5))

            except Exception as e:
                self.logger.info(f"Could not visit Google Images: {str(e)}")

        except Exception as e:
            self.logger.error(f"Error in enhanced explore_google_services: {str(e)}")







    def _interact_with_gmail_enhanced(self):
        """Phase 2: Interact with Gmail using enhanced behavioral patterns"""
        self.logger.info("Phase 2: Interacting with Gmail (Enhanced)...")

        try:
            # Navigate to Gmail
            self.go("https://mail.google.com")
            sleep(uniform(3.0, 5.0))

            # Wait for Gmail to load with enhanced waiting and multiple fallbacks
            gmail_loaded = False
            try:
                self.wait_for_element_present(By.XPATH, '//div[@role="main"]', timeout=15)
                gmail_loaded = True
            except:
                try:
                    self.wait_for_element_present(By.CSS_SELECTOR, '[role="navigation"]', timeout=10)
                    gmail_loaded = True
                except:
                    try:
                        self.wait_for_element_present(By.XPATH, '//img[@alt="Gmail"]', timeout=10)
                        gmail_loaded = True
                    except:
                        self.logger.info("Gmail interface not fully loaded, continuing with basic interaction...")

            # Handle Gmail popups and welcome screens
            self._handle_gmail_popups_enhanced()

            # Simulate reading emails with human-like scrolling
            self.human_scroll_page('down', randint(8, 15))
            sleep(uniform(2.0, 4.0))

            # Try to interact with Gmail interface elements using enhanced behavior
            self._interact_with_gmail_compose_enhanced()
            self._interact_with_gmail_navigation_enhanced()

        except Exception as e:
            self.logger.error(f"Error in enhanced interact_with_gmail: {str(e)}")

    def _handle_gmail_popups_enhanced(self):
        """Handle Gmail popups with enhanced detection"""
        try:
            popup_selectors = [
                (By.XPATH, '//button[contains(text(), "Continue")]'),
                (By.XPATH, '//button[contains(text(), "Continuer")]'),
                (By.XPATH, '//button[contains(text(), "Continue as")]'),
                (By.XPATH, '//button[contains(text(), "Continuer en tant que")]'),
                (By.XPATH, '//button[contains(text(), "Got it")]'),
                (By.XPATH, '//button[contains(text(), "OK")]'),
                (By.XPATH, '//button[contains(text(), "Compris")]'),
                (By.XPATH, '//button[@aria-label="Close"]'),
                (By.XPATH, '//button[@aria-label="Fermer"]'),
                (By.XPATH, '//button[contains(text(), "Skip")]'),
                (By.XPATH, '//button[contains(text(), "Ignorer")]')
            ]

            for by, selector in popup_selectors:
                try:
                    elements = self.find_elements(by, selector)
                    if elements:
                        popup_button = elements[0]
                        self.logger.info(f"Found Gmail popup button: {selector}")
                        self.human_click_element(popup_button)
                        sleep(uniform(1.0, 2.0))
                        break
                except:
                    continue

        except Exception as e:
            self.logger.info(f"Could not handle Gmail popups: {str(e)}")

    def _interact_with_gmail_compose_enhanced(self):
        """Interact with Gmail compose button using enhanced detection"""
        try:
            compose_selectors = [
                (By.XPATH, '//div[@role="button" and contains(text(), "Compose")]'),
                (By.XPATH, '//div[@role="button" and contains(text(), "Écrire")]'),
                (By.XPATH, '//div[contains(@class, "T-I") and contains(@class, "T-I-KE")]'),
                (By.XPATH, '//div[@gh="cm"]'),
                (By.XPATH, '//div[contains(@aria-label, "Compose")]'),
                (By.XPATH, '//div[contains(@aria-label, "Écrire")]')
            ]

            for by, selector in compose_selectors:
                try:
                    elements = self.find_elements(by, selector)
                    if elements:
                        compose_button = elements[0]
                        self.logger.info(f"Found compose button: {selector}")
                        # Hover over compose button with human-like movement
                        self.human_move_to_element(compose_button)
                        sleep(uniform(1.0, 2.0))
                        return
                except:
                    continue

            self.logger.info("Could not find Gmail compose button with any selector")

        except Exception as e:
            self.logger.info(f"Could not interact with compose button: {str(e)}")

    def _interact_with_gmail_navigation_enhanced(self):
        """Interact with Gmail navigation using enhanced patterns"""
        try:
            # Try different navigation strategies
            navigation_success = False

            # Strategy 1: Try sidebar navigation
            if not navigation_success:
                navigation_success = self._click_gmail_sidebar_enhanced()

            # Strategy 2: Try toolbar interaction
            if not navigation_success:
                navigation_success = self._interact_with_gmail_toolbar_enhanced()

            # Strategy 3: Fallback to basic scrolling
            if not navigation_success:
                self._perform_basic_gmail_scrolling_enhanced()

        except Exception as e:
            self.logger.info(f"Could not interact with Gmail navigation: {str(e)}")

    def _click_gmail_sidebar_enhanced(self):
        """Click Gmail sidebar items with enhanced detection"""
        try:
            sidebar_selectors = [
                (By.XPATH, '//a[contains(@href, "#sent")]'),
                (By.XPATH, '//div[@role="button" and contains(text(), "Sent")]'),
                (By.XPATH, '//div[@role="button" and contains(text(), "Envoyés")]'),
                (By.XPATH, '//a[contains(@href, "#drafts")]'),
                (By.XPATH, '//div[@role="button" and contains(text(), "Drafts")]'),
                (By.XPATH, '//div[@role="button" and contains(text(), "Brouillons")]'),
                (By.XPATH, '//a[contains(@href, "#spam")]'),
                (By.XPATH, '//div[@role="button" and contains(text(), "Spam")]'),
                (By.XPATH, '//a[contains(@href, "#all")]'),
                (By.XPATH, '//div[@role="button" and contains(text(), "All Mail")]'),
                (By.XPATH, '//div[@role="button" and contains(text(), "Tous les messages")]')
            ]

            for by, selector in sidebar_selectors:
                try:
                    elements = self.find_elements(by, selector)
                    if elements:
                        element = elements[0]
                        self.logger.info(f"Clicking Gmail sidebar item: {selector}")
                        self.human_click_element(element)
                        sleep(uniform(2.0, 3.0))

                        # Scroll in the selected section with human-like behavior
                        self.human_scroll_page('down', randint(5, 10))
                        sleep(uniform(1.5, 2.5))
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.info(f"Could not click Gmail sidebar items: {str(e)}")
            return False

    def _interact_with_gmail_toolbar_enhanced(self):
        """Interact with Gmail toolbar using enhanced detection"""
        try:
            toolbar_selectors = [
                (By.XPATH, '//div[@role="button" and @aria-label]'),
                (By.XPATH, '//div[contains(@class, "T-I")]'),
                (By.XPATH, '//div[@gh]')
            ]

            for by, selector in toolbar_selectors:
                try:
                    elements = self.find_elements(by, selector)
                    if elements:
                        # Just hover over a random toolbar element (limit to first 3)
                        element = choice(elements[:3])
                        self.human_move_to_element(element)
                        sleep(uniform(0.5, 1.0))
                        return True
                except:
                    continue

            return False

        except Exception as e:
            self.logger.info(f"Could not interact with Gmail toolbar: {str(e)}")
            return False

    def _perform_basic_gmail_scrolling_enhanced(self):
        """Perform basic Gmail scrolling with enhanced human-like patterns"""
        try:
            self.logger.info("Performing enhanced Gmail scrolling as fallback...")

            # Human-like scrolling pattern
            self.human_scroll_page('down', randint(8, 15))
            sleep(uniform(1.0, 2.0))

            # Scroll back up a bit
            self.human_scroll_page('up', randint(3, 6))
            sleep(uniform(1.0, 2.0))

            # Final scroll down
            self.human_scroll_page('down', randint(2, 5))
            sleep(uniform(1.0, 2.0))

            return True

        except Exception as e:
            self.logger.info(f"Enhanced Gmail scrolling failed: {str(e)}")
            return False

    def _perform_search_activities_enhanced(self):
        """Phase 3: Perform realistic search activities with enhanced behavior"""
        self.logger.info("Phase 3: Performing search activities (Enhanced)...")

        try:
            # Go back to Google search
            self.go("https://www.google.com")
            sleep(uniform(2.0, 3.0))

            # Perform 2-3 realistic searches with enhanced typing
            search_queries = [
                "weather today",
                "news france",
                "restaurants near me",
                "how to cook pasta",
                "best movies 2024",
                "travel destinations europe",
                "healthy recipes",
                "technology news",
                "sports results",
                "online shopping"
            ]

            num_searches = randint(2, 3)
            selected_queries = random.sample(search_queries, num_searches)

            for query in selected_queries:
                try:
                    # Find search box
                    search_elements = self.find_elements(By.XPATH, '//input[@name="q"]')
                    if search_elements:
                        search_box = search_elements[0]

                        # Clear and type search query with human-like typing
                        search_box.clear()
                        sleep(uniform(0.5, 1.0))

                        self.human_type_text(search_box, query, clear_first=False)
                        sleep(uniform(1.0, 2.0))

                        # Press Enter
                        from selenium.webdriver.common.keys import Keys
                        search_box.send_keys(Keys.ENTER)
                        sleep(uniform(2.0, 4.0))

                        # Simulate reading search results with human-like scrolling
                        self.human_scroll_page('down', randint(8, 15))
                        sleep(uniform(2.0, 3.0))

                        # Sometimes click on a search result
                        if randint(1, 3) == 1:  # 33% chance
                            try:
                                # Find first few search results
                                result_elements = self.find_elements(By.XPATH, '//h3/parent::a')[:3]
                                if result_elements:
                                    selected_result = choice(result_elements)
                                    self.human_click_element(selected_result)
                                    sleep(uniform(3.0, 5.0))

                                    # Simulate reading the page
                                    self.human_scroll_page('down', randint(5, 12))
                                    sleep(uniform(2.0, 4.0))

                                    # Go back to search results
                                    self.back()
                                    sleep(uniform(1.5, 2.5))

                            except Exception as e:
                                self.logger.info(f"Could not click on search result: {str(e)}")

                        # Go back to Google homepage for next search
                        if query != selected_queries[-1]:  # Not the last search
                            self.go("https://www.google.com")
                            sleep(uniform(1.5, 2.5))

                except Exception as e:
                    self.logger.info(f"Could not perform search for '{query}': {str(e)}")

        except Exception as e:
            self.logger.error(f"Error in enhanced perform_search_activities: {str(e)}")

    def _check_account_settings_enhanced(self):
        """Phase 4: Check account settings with enhanced behavioral simulation"""
        self.logger.info("Phase 4: Checking account settings (Enhanced)...")

        try:
            # Navigate to Google Account settings
            self.go("https://myaccount.google.com")
            sleep(uniform(3.0, 5.0))

            # Simulate browsing account settings with human-like scrolling
            self.human_scroll_page('down', randint(8, 15))
            sleep(uniform(2.0, 3.0))

            # Try to interact with different sections using enhanced behavior
            try:
                sections = ['Personal info', 'Data & privacy', 'Security']
                for section in sections:
                    try:
                        section_elements = self.find_elements(By.XPATH, f'//h2[contains(text(), "{section}")]')
                        if section_elements:
                            section_element = section_elements[0]
                            self.human_move_to_element(section_element)
                            sleep(uniform(1.0, 2.0))
                            break
                    except:
                        continue

            except Exception as e:
                self.logger.info(f"Could not interact with account sections: {str(e)}")

            # Scroll more to simulate reading with enhanced behavior
            self.human_scroll_page('down', randint(5, 10))
            sleep(uniform(2.0, 3.0))

        except Exception as e:
            self.logger.error(f"Error in enhanced check_account_settings: {str(e)}")

    def _return_to_google_home_enhanced(self):
        """Phase 5: Return to Google homepage with enhanced behavior"""
        self.logger.info("Phase 5: Returning to Google homepage (Enhanced)...")

        try:
            self.go("https://www.google.com")
            sleep(uniform(2.0, 3.0))

            # Final scroll to simulate one last look with human-like behavior
            self.human_scroll_page('down', randint(3, 6))
            sleep(uniform(1.0, 2.0))

        except Exception as e:
            self.logger.error(f"Error in enhanced return_to_google_home: {str(e)}")

    def _set_window_size_from_profile(self):
        """Set browser window size based on profile's screen resolution and user agent"""
        try:
            # Get profile fingerprint
            profile_config = self.profile_manager.get_profile(self.email)
            fingerprint = profile_config.get('fingerprint', {})
            screen_resolution = fingerprint.get('screen_resolution', {})
            user_agent = fingerprint.get('user_agent', '')

            if screen_resolution:
                width = screen_resolution.get('width', 1920)
                height = screen_resolution.get('height', 1080)
                device_type = screen_resolution.get('device_type', 'desktop')

                self.logger.info(f"Setting window size based on profile: {width}x{height} ({device_type})")

                # Adjust window size based on device type
                if device_type == 'mobile':
                    # For mobile, we simulate mobile viewport but in desktop browser
                    # Use a reasonable desktop window size but set mobile viewport
                    window_width = min(width * 2, 800)  # Scale up for desktop window
                    window_height = min(height * 2, 1200)

                    # Set desktop window size using appropriate method for SB context manager or regular WebDriver
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                        self.browser.driver.set_window_size(window_width, window_height)
                    elif hasattr(self.browser, 'set_window_size'):
                        self.browser.set_window_size(window_width, window_height)

                    # Set mobile viewport using JavaScript with appropriate method for SB context manager or regular WebDriver
                    mobile_viewport_js = f"""
                        // Set mobile viewport
                        Object.defineProperty(window.screen, 'width', {{
                            writable: false,
                            configurable: false,
                            value: {width}
                        }});
                        Object.defineProperty(window.screen, 'height', {{
                            writable: false,
                            configurable: false,
                            value: {height}
                        }});
                        Object.defineProperty(window.screen, 'availWidth', {{
                            writable: false,
                            configurable: false,
                            value: {width}
                        }});
                        Object.defineProperty(window.screen, 'availHeight', {{
                            writable: false,
                            configurable: false,
                            value: {height - 20}  // Account for status bar
                        }});
                    """

                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'execute_script'):
                        self.browser.driver.execute_script(mobile_viewport_js)
                    elif hasattr(self.browser, 'execute_script'):
                        self.browser.execute_script(mobile_viewport_js)

                elif device_type == 'tablet':
                    # For tablets, use the actual resolution but ensure minimum desktop size
                    window_width = max(width, 800)
                    window_height = max(height, 600)
                    # Set tablet window size using appropriate method for SB context manager or regular WebDriver
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                        self.browser.driver.set_window_size(window_width, window_height)
                    elif hasattr(self.browser, 'set_window_size'):
                        self.browser.set_window_size(window_width, window_height)

                else:  # desktop
                    # For desktop, use the actual screen resolution
                    # But ensure it's not larger than actual screen
                    import tkinter as tk
                    try:
                        root = tk.Tk()
                        screen_width = root.winfo_screenwidth()
                        screen_height = root.winfo_screenheight()
                        root.destroy()

                        # Don't exceed actual screen size
                        window_width = min(width, screen_width - 100)
                        window_height = min(height, screen_height - 100)
                    except:
                        # Fallback if tkinter not available
                        window_width = min(width, 1920)
                        window_height = min(height, 1080)

                    # Set desktop window size using appropriate method for SB context manager or regular WebDriver
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                        self.browser.driver.set_window_size(window_width, window_height)
                    elif hasattr(self.browser, 'set_window_size'):
                        self.browser.set_window_size(window_width, window_height)

                # Also set the screen object properties to match
                self._set_screen_properties(screen_resolution)

                self.logger.info(f"Window size set to: {window_width}x{window_height}")

            else:
                # Fallback to default size based on user agent
                self._set_default_window_size_from_user_agent(user_agent)

        except Exception as e:
            self.logger.error(f"Error setting window size from profile: {str(e)}")
            # Fallback to default size
            try:
                # Set fallback window size using appropriate method for SB context manager or regular WebDriver
                if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                    self.browser.driver.set_window_size(1366, 768)
                elif hasattr(self.browser, 'set_window_size'):
                    self.browser.set_window_size(1366, 768)
            except:
                pass



    def _choose_random_window_size(self):
        """Randomly choose between full window size and fixed custom window size"""
        choices = ['fullscreen', 'custom']
        choice = random.choice(choices)

        if choice == 'fullscreen':
            # Maximize window or set to screen resolution
            try:
                if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'maximize_window'):
                    self.browser.driver.maximize_window()
                elif hasattr(self.browser, 'maximize_window'):
                    self.browser.maximize_window()

                self.logger.info("Window size set to: FULLSCREEN")
                return 'fullscreen', None, None

            except Exception as e:
                self.logger.warning(f"Could not maximize window: {e}")
                # Fallback to screen resolution
                import tkinter as tk
                try:
                    root = tk.Tk()
                    width = root.winfo_screenwidth()
                    height = root.winfo_screenheight()
                    root.destroy()

                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                        self.browser.driver.set_window_size(width, height)
                    elif hasattr(self.browser, 'set_window_size'):
                        self.browser.set_window_size(width, height)

                    return 'fullscreen', width, height
                except:
                    return 'fullscreen', 1920, 1080

        else:  # custom
            width, height = (1366, 768)

            try:
                if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                    self.browser.driver.set_window_size(width, height)
                elif hasattr(self.browser, 'set_window_size'):
                    self.browser.set_window_size(width, height)

                self.logger.info(f"Window size set to: CUSTOM {width}x{height}")
                return 'custom', width, height

            except Exception as e:
                self.logger.error(f"Could not set custom window size: {e}")
                return 'custom', width, height

    def _choose_random_window_size_for_sb_driver(self):
        """Randomly choose between full window size and fixed custom window size for SeleniumBase driver"""
        choices = ['fullscreen', 'custom']
        choice = random.choice(choices)

        if choice == 'fullscreen':
            try:
                if hasattr(self.sb_driver, 'driver') and hasattr(self.sb_driver.driver, 'maximize_window'):
                    self.sb_driver.driver.maximize_window()
                elif hasattr(self.sb_driver, 'maximize_window'):
                    self.sb_driver.maximize_window()

                self.logger.info("Window size set to: FULLSCREEN")
                return 'fullscreen', None, None

            except Exception as e:
                self.logger.warning(f"Could not maximize window: {e}")
                # Fallback to screen resolution
                import tkinter as tk
                try:
                    root = tk.Tk()
                    width = root.winfo_screenwidth()
                    height = root.winfo_screenheight()
                    root.destroy()

                    if hasattr(self.sb_driver, 'driver') and hasattr(self.sb_driver.driver, 'set_window_size'):
                        self.sb_driver.driver.set_window_size(width, height)
                    elif hasattr(self.sb_driver, 'set_window_size'):
                        self.sb_driver.set_window_size(width, height)

                    return 'fullscreen', width, height
                except:
                    return 'fullscreen', 1920, 1080

        else:  # custom
            width, height = (1366, 768)

            try:
                if hasattr(self.sb_driver, 'driver') and hasattr(self.sb_driver.driver, 'set_window_size'):
                    self.sb_driver.driver.set_window_size(width, height)
                elif hasattr(self.sb_driver, 'set_window_size'):
                    self.sb_driver.set_window_size(width, height)

                self.logger.info(f"Window size set to: CUSTOM {width}x{height}")
                return 'custom', width, height

            except Exception as e:
                self.logger.error(f"Could not set custom window size: {e}")
                return 'custom', width, height
        
    def _set_screen_properties(self, screen_resolution):
        """Set screen properties in JavaScript to match the profile"""
        try:
            width = screen_resolution.get('width', 1920)
            height = screen_resolution.get('height', 1080)

            # Get screen properties from fingerprint
            profile_config = self.profile_manager.get_profile(self.email)
            fingerprint = profile_config.get('fingerprint', {})
            screen_properties = fingerprint.get('screen_properties', {})

            color_depth = screen_properties.get('colorDepth', 24)
            pixel_depth = screen_properties.get('pixelDepth', 24)
            device_pixel_ratio = screen_properties.get('devicePixelRatio', 1.0)

            # Override screen properties using appropriate method for SB context manager or regular WebDriver
            screen_properties_js = f"""
                // Override screen properties to match profile - check if properties can be redefined
                try {{
                    const colorDepthDescriptor = Object.getOwnPropertyDescriptor(window.screen, 'colorDepth');
                    if (!colorDepthDescriptor || colorDepthDescriptor.configurable !== false) {{
                        Object.defineProperty(window.screen, 'colorDepth', {{
                            writable: false,
                            configurable: true,
                            value: {color_depth}
                        }});
                    }}
                }} catch (e) {{
                    // Cannot redefine colorDepth, skip
                }}

                try {{
                    const pixelDepthDescriptor = Object.getOwnPropertyDescriptor(window.screen, 'pixelDepth');
                    if (!pixelDepthDescriptor || pixelDepthDescriptor.configurable !== false) {{
                        Object.defineProperty(window.screen, 'pixelDepth', {{
                            writable: false,
                            configurable: true,
                            value: {pixel_depth}
                        }});
                    }}
                }} catch (e) {{
                    // Cannot redefine pixelDepth, skip
                }}

                try {{
                    const devicePixelRatioDescriptor = Object.getOwnPropertyDescriptor(window, 'devicePixelRatio');
                    if (!devicePixelRatioDescriptor || devicePixelRatioDescriptor.configurable !== false) {{
                        Object.defineProperty(window, 'devicePixelRatio', {{
                            writable: false,
                            configurable: true,
                            value: {device_pixel_ratio}
                        }});
                    }}
                }} catch (e) {{
                    // Cannot redefine devicePixelRatio, skip
                }}

                // Set consistent screen dimensions
                try {{
                    const widthDescriptor = Object.getOwnPropertyDescriptor(window.screen, 'width');
                    if (!widthDescriptor || widthDescriptor.configurable !== false) {{
                        Object.defineProperty(window.screen, 'width', {{
                            writable: false,
                            configurable: true,
                            value: {width}
                        }});
                    }}
                }} catch (e) {{
                    // Cannot redefine width, skip
                }}

                try {{
                    const heightDescriptor = Object.getOwnPropertyDescriptor(window.screen, 'height');
                    if (!heightDescriptor || heightDescriptor.configurable !== false) {{
                        Object.defineProperty(window.screen, 'height', {{
                            writable: false,
                            configurable: true,
                            value: {height}
                        }});
                    }}
                }} catch (e) {{
                    // Cannot redefine height, skip
                }}
            """

            if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'execute_script'):
                self.browser.driver.execute_script(screen_properties_js)
            elif hasattr(self.browser, 'execute_script'):
                self.browser.execute_script(screen_properties_js)

            self.logger.info(f"Screen properties set: {width}x{height}, {color_depth}-bit color, DPR: {device_pixel_ratio}")

        except Exception as e:
            self.logger.warning(f"Error setting screen properties (non-critical): {str(e)}")

    def _set_default_window_size_from_user_agent(self, user_agent):
        """Set default window size based on user agent when no profile resolution available"""
        try:
            if user_agent:
                ua_lower = user_agent.lower()

                if 'mobile' in ua_lower or 'android' in ua_lower or 'iphone' in ua_lower:
                    # Mobile user agent - use mobile-friendly desktop window
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                        self.browser.driver.set_window_size(414, 736)  # iPhone Plus size
                    elif hasattr(self.browser, 'set_window_size'):
                        self.browser.set_window_size(414, 736)  # iPhone Plus size
                elif 'ipad' in ua_lower or 'tablet' in ua_lower:
                    # Tablet user agent
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                        self.browser.driver.set_window_size(768, 1024)  # iPad size
                    elif hasattr(self.browser, 'set_window_size'):
                        self.browser.set_window_size(768, 1024)  # iPad size
                elif 'macintosh' in ua_lower or 'mac os x' in ua_lower:
                    # Mac user agent - typically higher resolution
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                        self.browser.driver.set_window_size(1440, 900)  # Common Mac resolution
                    elif hasattr(self.browser, 'set_window_size'):
                        self.browser.set_window_size(1440, 900)  # Common Mac resolution
                else:
                    # Default desktop
                    if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                        self.browser.driver.set_window_size(1366, 768)  # Most common desktop resolution
                    elif hasattr(self.browser, 'set_window_size'):
                        self.browser.set_window_size(1366, 768)  # Most common desktop resolution
            else:
                # No user agent info, use default
                if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'set_window_size'):
                    self.browser.driver.set_window_size(1366, 768)
                elif hasattr(self.browser, 'set_window_size'):
                    self.browser.set_window_size(1366, 768)

            self.logger.info(f"Default window size set based on user agent")

        except Exception as e:
            self.logger.error(f"Error setting default window size: {str(e)}")

    def get_behavioral_profile(self):
        """Get current behavioral simulation profile characteristics"""
        try:
            if hasattr(self, 'behavioral_simulator'):
                return {
                    'profile_seed': self.behavioral_simulator.profile_seed,
                    'mouse_speed_base': self.behavioral_simulator.mouse_speed_base,
                    'mouse_precision': self.behavioral_simulator.mouse_precision,
                    'typing_speed_wpm': self.behavioral_simulator.typing_speed_wpm,
                    'typing_accuracy': self.behavioral_simulator.typing_accuracy,
                    'scroll_speed_base': self.behavioral_simulator.scroll_speed_base,
                    'click_duration_base': self.behavioral_simulator.click_duration_base,
                    'pre_click_hover_time': self.behavioral_simulator.pre_click_hover_time
                }
            else:
                return {'error': 'Behavioral simulator not available'}
        except Exception as e:
            self.logger.error(f"Error getting behavioral profile: {str(e)}")
            return {'error': str(e)}

    # ========== BROWSER CONTROL METHODS ==========

    def quit(self):
        """Quit the browser and clean up resources"""
        try:
            if hasattr(self, 'browser') and self.browser:
                # Check if this is an SB context manager (BaseCase)
                if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'quit'):
                    # This is an SB context manager - quit the underlying driver
                    self.browser.driver.quit()
                    self.logger.info("SB context manager browser quit successfully")
                elif hasattr(self.browser, 'quit'):
                    # This is a regular WebDriver
                    self.browser.quit()
                    self.logger.info("Browser quit successfully")
                else:
                    self.logger.warning("Browser object doesn't have quit method")

            # Also clean up SB context if it exists
            self.cleanup_sb_context()

        except Exception as e:
            self.logger.error(f"Error quitting browser: {str(e)}")



    def close(self):
        """Close the current browser window"""
        try:
            if hasattr(self, 'browser') and self.browser:
                # Check if this is an SB context manager (BaseCase)
                if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'close'):
                    # This is an SB context manager - close the underlying driver
                    self.browser.driver.close()
                    self.logger.info("SB context manager browser window closed successfully")
                elif hasattr(self.browser, 'close'):
                    # This is a regular WebDriver
                    self.browser.close()
                    self.logger.info("Browser window closed successfully")
                else:
                    self.logger.warning("Browser object doesn't have close method")
        except Exception as e:
            self.logger.error(f"Error closing browser window: {str(e)}")

    def refresh(self):
        """Refresh the current page"""
        try:
            if hasattr(self, 'browser') and self.browser:
                self.browser.refresh()
                self.logger.info("Page refreshed successfully")
        except Exception as e:
            self.logger.error(f"Error refreshing page: {str(e)}")

    def back(self):
        """Navigate back in browser history"""
        try:
            if hasattr(self, 'browser') and self.browser:
                self.browser.back()
                self.logger.info("Navigated back successfully")
        except Exception as e:
            self.logger.error(f"Error navigating back: {str(e)}")

    def forward(self):
        """Navigate forward in browser history"""
        try:
            if hasattr(self, 'browser') and self.browser:
                self.browser.forward()
                self.logger.info("Navigated forward successfully")
        except Exception as e:
            self.logger.error(f"Error navigating forward: {str(e)}")

    def get_current_url(self):
        """Get the current URL"""
        try:
            if hasattr(self, 'browser') and self.browser:
                # Get current URL using appropriate method for SB context manager or regular WebDriver
                if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'current_url'):
                    return self.browser.driver.current_url
                elif hasattr(self.browser, 'current_url'):
                    return self.browser.current_url
            return None
        except Exception as e:
            self.logger.error(f"Error getting current URL: {str(e)}")
            return None

    def get_title(self):
        """Get the current page title"""
        try:
            if hasattr(self, 'browser') and self.browser:
                # Get page title using appropriate method for SB context manager or regular WebDriver
                if hasattr(self.browser, 'driver') and hasattr(self.browser.driver, 'title'):
                    return self.browser.driver.title
                elif hasattr(self.browser, 'title'):
                    return self.browser.title
            return None
        except Exception as e:
            self.logger.error(f"Error getting page title: {str(e)}")
            return None


# Alias for backward compatibility
Driver = EnhancedSeleniumBaseDriver

